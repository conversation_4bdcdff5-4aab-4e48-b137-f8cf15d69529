package com.firstedu.marsladder.falcon.reward.service.domain

import com.firstedu.marsladder.falcon.practice.PracticeMode
import com.firstedu.marsladder.falcon.practice.PracticeStatus
import com.firstedu.marsladder.falcon.practice.repository.entity.PracticeEntity

data class PracticeHistoricalJob(
    val practices: List<PracticeEntity>,
    val minimumPracticeLimitation: Int,
) {
    fun shouldBeDescendingStar(areaOfStudyId: String) =
        practices.filter {
            it.mode == PracticeMode.BATTLE &&
                it.status == PracticeStatus.FINISHED &&
                it.metadata.areaId == areaOfStudyId
        }.size < minimumPracticeLimitation
}

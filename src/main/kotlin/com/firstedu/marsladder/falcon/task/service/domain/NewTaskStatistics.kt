package com.firstedu.marsladder.falcon.task.service.domain

import com.firstedu.marsladder.falcon.task.repository.entity.ClassroomStatisticsEntity
import com.firstedu.marsladder.falcon.task.repository.entity.ClassroomStudentStatisticsEntity
import com.firstedu.marsladder.falcon.task.repository.entity.StudentTaskStatisticsEntity
import java.time.LocalDateTime

data class PracticeStatistics(
    val totalQuestionCount: Int,
    val answeredQuestionCount: Int,
    val correctQuestionCount: Int,
    val spentSeconds: Int,
)

data class ClassroomStudentStatistics(
    val studentId: String,
    val classroomId: String,
    val totalTaskCount: Int,
    val completedTaskCount: Int,
    val totalQuestionCount: Int,
    val answeredQuestionCount: Int,
    val correctQuestionCount: Int,
    val totalSpentSeconds: Int,
) {
    companion object {
        fun from(entity: ClassroomStudentStatisticsEntity) =
            ClassroomStudentStatistics(
                studentId = entity.studentId,
                classroomId = entity.classroomId,
                totalTaskCount = entity.totalTaskCount,
                completedTaskCount = entity.completedTaskCount,
                totalQuestionCount = entity.totalQuestionCount,
                answeredQuestionCount = entity.answeredQuestionCount,
                correctQuestionCount = entity.correctQuestionCount,
                totalSpentSeconds = entity.totalSpentSeconds,
            )
    }
}

data class ClassroomStatistics(
    val totalSetCount: Int,
    val completedSetCount: Int,
    val totalQuestionCount: Int,
    val correctQuestionCount: Int,
    val totalTaskCount: Int,
) {
    companion object {
        fun from(
            entity: ClassroomStatisticsEntity,
            totalTaskCount: Int,
        ) =
            ClassroomStatistics(
                totalSetCount = entity.totalSetCount,
                completedSetCount = entity.completedSetCount,
                totalQuestionCount = entity.totalQuestionCount,
                correctQuestionCount = entity.correctQuestionCount,
                totalTaskCount = totalTaskCount,
            )
    }
}

fun ClassroomStatistics.averageAccuracy(): Double {
    return when {
        totalQuestionCount != 0 -> correctQuestionCount.toDouble() / totalQuestionCount
        else -> 0.0
    }
}

fun ClassroomStatistics.completionRate(): Double {
    return when {
        totalSetCount != 0 -> completedSetCount.toDouble() / totalSetCount
        else -> 0.0
    }
}

data class MultisubjectClassroomStatistics(
    val totalStudentTaskCount: Int,
    val completedStudentTaskCount: Int,
    val taskTotalScore: Double,
    val studentAverageScore: Double,
    val totalTaskCount: Int,
)

fun MultisubjectClassroomStatistics.averageAccuracy(): Double {
    return when {
        taskTotalScore != 0.0 -> studentAverageScore / taskTotalScore
        else -> 0.0
    }
}

fun MultisubjectClassroomStatistics.completionRate(): Double {
    return when {
        totalStudentTaskCount != 0 -> completedStudentTaskCount.toDouble() / totalStudentTaskCount
        else -> 0.0
    }
}

data class NewStudentTaskStatistics(
    val studentId: String,
    val taskId: String,
    val studentTaskId: String,
    val taskCompletedAt: LocalDateTime,
    val totalSetCount: Int,
    val completedSetCount: Int,
    val totalQuestionCount: Int,
    val correctQuestionCount: Int,
    val spentSeconds: Int,
) {
    companion object {
        fun from(entity: StudentTaskStatisticsEntity) =
            NewStudentTaskStatistics(
                studentId = entity.studentId,
                taskId = entity.taskId,
                studentTaskId = entity.studentTaskId,
                taskCompletedAt = entity.taskCompletedAt,
                totalSetCount = entity.totalSetCount,
                completedSetCount = entity.completedSetCount,
                totalQuestionCount = entity.totalQuestionCount,
                correctQuestionCount = entity.correctQuestionCount,
                spentSeconds = entity.spentSeconds,
            )
    }
}

package com.firstedu.marsladder.falcon.task.repository.entity

import com.firstedu.marsladder.falcon.practice.repository.entity.QuestionOption
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeQuestion
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeQuestionOption
import com.firstedu.marsladder.falcon.question.QuestionCAS
import com.firstedu.marsladder.falcon.question.QuestionDifficulty
import com.firstedu.marsladder.falcon.client.fuxi.dto.PublishedQuestion
import java.util.UUID

data class TaskPreviewQuestionEntity(
    val id: String?,
    val publishedQuestionId: String?,
    val originalQuestionId: String,
    val originalQuestionVersion: Int = 1,
    val topic: String,
    val answerAnalysis: String,
    val options: List<QuestionOption>,
    val difficulty: QuestionDifficulty,
    val cas: QuestionCAS,
    val approved: Boolean? = false,
) {
    fun toPracticeQuestion() =
        PracticeQuestion(
            id = this.id,
            originalQuestionId = this.originalQuestionId,
            publishedQuestionId = this.publishedQuestionId ?: "",
            originalQuestionVersion = this.originalQuestionVersion,
            topic = this.topic,
            answerAnalysis = this.answerAnalysis,
            questionOptions = this.options.map { PracticeQuestionOption(it.id, it.value, it.correct) },
            difficulty = this.difficulty,
            cas = this.cas,
        )

    companion object {
        fun from(question: PublishedQuestion) =
            TaskPreviewQuestionEntity(
                id = question.originalQuestionId,
                publishedQuestionId = question.id,
                originalQuestionId = question.originalQuestionId,
                originalQuestionVersion = question.originalQuestionVersion,
                topic = question.topic ?: "",
                answerAnalysis = question.answerAnalysis ?: "",
                options = question.options?.map { QuestionOption(UUID.randomUUID().toString(), it.value, it.correct) } ?: emptyList(),
                difficulty = question.difficulty,
                cas = question.cas,
                approved = false,
            )

        fun from(question: PracticeQuestion) =
            TaskPreviewQuestionEntity(
                id = question.originalQuestionId,
                publishedQuestionId = question.publishedQuestionId,
                originalQuestionId = question.originalQuestionId,
                originalQuestionVersion = question.originalQuestionVersion,
                topic = question.topic,
                answerAnalysis = question.answerAnalysis,
                options = question.questionOptions.map { QuestionOption(it.id ?: "", it.value, it.correct) },
                difficulty = question.difficulty,
                cas = question.cas,
                approved = false,
            )
    }
}

package com.firstedu.marsladder.falcon.task.controller.dto

import com.firstedu.marsladder.falcon.task.service.domain.OfflineTaskUsage

data class OfflineTaskUsageResponseDto(
    val availableCount: Int,
    val usedCount: Int,
) {
    companion object {
        fun from(offlineTaskUsage: OfflineTaskUsage) =
            OfflineTaskUsageResponseDto(
                availableCount = offlineTaskUsage.availableCount,
                usedCount = offlineTaskUsage.usedCount,
            )
    }
}

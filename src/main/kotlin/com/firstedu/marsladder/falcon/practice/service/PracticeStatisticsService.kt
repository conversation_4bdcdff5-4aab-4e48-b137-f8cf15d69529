package com.firstedu.marsladder.falcon.practice.service

import com.firstedu.marsladder.falcon.practice.service.domain.ContentTagStatistics
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeDetails
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeStatisticsReport
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeSummary

interface PracticeStatisticsService {
    fun getPracticeHistoricalStatisticsReport(
        cognitoUid: String,
        courseId: String,
        historicalDays: Long?,
        zoneId: String?,
    ): PracticeStatisticsReport

    fun getTop7PerformanceContentTags(
        cognitoUid: String,
        courseId: String,
        historicalDays: Long?,
    ): List<ContentTagStatistics>

    fun getPracticeSummary(
        cognitoUid: String,
        courseId: String,
    ): List<PracticeSummary>

    fun calculatePracticeStatistics(practiceDetails: PracticeDetails)
}

package com.firstedu.marsladder.falcon.practice.service.strategy

import com.firstedu.marsladder.falcon.client.fuxi.FuxiClient
import com.firstedu.marsladder.falcon.infrastructure.aws.lambda.LambdaClient
import com.firstedu.marsladder.falcon.practice.PracticeConstants.INCLUDE
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeMetadata
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeQuestion
import com.firstedu.marsladder.falcon.practice.service.domain.QuestionGeneratorConfig
import com.firstedu.marsladder.falcon.question.QuestionDifficulty
import com.firstedu.marsladder.falcon.client.fuxi.dto.PublishedQuestion
import com.firstedu.marsladder.falcon.utils.getObjectMapper
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component(INCLUDE)
class IncludeQuestionGenerateStrategy(
    private val fuxiClient: FuxiClient,
    private val lambdaClient: LambdaClient,
) : QuestionGenerateStrategy {
    @Transactional(readOnly = true)
    override fun generate(
        metadata: PracticeMetadata,
        config: QuestionGeneratorConfig,
    ): List<PracticeQuestion> {
        val questions = mutableListOf<PublishedQuestion>()
        if (config.excludeQuestionIds.isEmpty()) {
            questions.addAll(
                fuxiClient.findSubTopicsIncludedQuestions(
                    getObjectMapper().writeValueAsString(metadata.topicIds.flatMap { topic -> topic.subTopicIds.map { it } }),
                    metadata.courseId,
                    config.easyQuestionCount,
                    config.normalQuestionCount,
                    config.hardQuestionCount,
                ),
            )
        } else {
            questions.addAll(
                fuxiClient.findSubTopicsIncludedQuestionsAndOriginalQuestionIdNotIn(
                    getObjectMapper().writeValueAsString(metadata.topicIds.flatMap { topic -> topic.subTopicIds.map { it } }),
                    metadata.courseId,
                    config.easyQuestionCount,
                    config.normalQuestionCount,
                    config.hardQuestionCount,
                    config.excludeQuestionIds,
                ),
            )

            if (!config.strictExclude && questions.size < config.easyQuestionCount + config.normalQuestionCount + config.hardQuestionCount) {
                questions.addAll(
                    fuxiClient.findSubTopicsIncludedQuestionsAndOriginalQuestionIdIn(
                        getObjectMapper().writeValueAsString(metadata.topicIds.flatMap { topic -> topic.subTopicIds.map { it } }),
                        metadata.courseId,
                        config.easyQuestionCount - questions.filter { it.difficulty == QuestionDifficulty.EASY }.size,
                        config.normalQuestionCount - questions.filter { it.difficulty == QuestionDifficulty.NORMAL }.size,
                        config.hardQuestionCount - questions.filter { it.difficulty == QuestionDifficulty.HARD }.size,
                        config.excludeQuestionIds,
                    ),
                )
            }
        }

        return questions.map {
            PracticeQuestion.from(it, lambdaClient)
        }
    }
}

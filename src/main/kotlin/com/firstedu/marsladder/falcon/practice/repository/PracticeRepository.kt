package com.firstedu.marsladder.falcon.practice.repository

import com.firstedu.marsladder.falcon.practice.PracticeStatus
import com.firstedu.marsladder.falcon.practice.repository.entity.PracticeEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.Optional

@Repository
interface PracticeRepository : JpaRepository<PracticeEntity, String> {
    @Query(
        """
        select * from practice 
        where :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        and mode not in ('CASUAL_TASK', 'CHALLENGE_TASK')
        order by start_at desc limit :limit
        """,
        nativeQuery = true,
    )
    fun findSelfPracticesByPlayerCognitoUidOrderByStartAtDesc(
        cognitoUid: String,
        limit: Int,
    ): List<PracticeEntity>

    @Query(
        """
        select * from practice 
        where :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        and mode = :mode
        order by start_at desc limit :limit
        """,
        nativeQuery = true,
    )
    fun findPracticesByPlayerCognitoUidAndModeOrderByStartAtDesc(
        cognitoUid: String,
        mode: String,
        limit: Int,
    ): List<PracticeEntity>

    @Query(
        """
        select * from practice 
        where :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        and mode = :mode
        and status = 'RUNNING'
        and TIMESTAMPADD(SECOND, total_time, start_at) > CURRENT_TIMESTAMP
        order by start_at desc
        limit 1
        """,
        nativeQuery = true,
    )
    fun findFirstRunningPracticesByModeAndPlayersCognitoUid(
        mode: String,
        cognitoUid: String,
    ): Optional<PracticeEntity>

    @Modifying(clearAutomatically = true)
    @Query(
        """
        update practice set players = 
        json_set(players, CONCAT(JSON_UNQUOTE(JSON_SEARCH(players->'${'$'}[*].cognitoUid', 'one', :cognitoUid)), '.gold'), :gold) 
        where id = :practiceId
    """,
        nativeQuery = true,
    )
    fun updatePlayerGold(
        practiceId: String,
        cognitoUid: String,
        gold: Int,
    )

    @Modifying(clearAutomatically = true)
    @Query(
        """
        update practice set players = 
        json_set(players, CONCAT(JSON_UNQUOTE(JSON_SEARCH(players->'${'$'}[*].cognitoUid', 'one', :cognitoUid)), '.experience'), :experience) 
        where id = :practiceId
    """,
        nativeQuery = true,
    )
    fun updatePlayerExperience(
        practiceId: String,
        cognitoUid: String,
        experience: Int,
    )

    @Modifying(clearAutomatically = true)
    @Query(
        """
        update practice set players =
        json_set(players, CONCAT(JSON_UNQUOTE(JSON_SEARCH(players->'${'$'}[*].cognitoUid', 'one', :cognitoUid)), '.star'), :star)
        where id = :practiceId
    """,
        nativeQuery = true,
    )
    fun updatePlayerStar(
        practiceId: String,
        cognitoUid: String,
        star: Int,
    )

    @Modifying(clearAutomatically = true)
    @Query(
        """
        update practice set players =
        json_set(players,
        CONCAT(JSON_UNQUOTE(JSON_SEARCH(players->'${'$'}[*].cognitoUid', 'one', :cognitoUid)), '.status'), :status,
        CONCAT(JSON_UNQUOTE(JSON_SEARCH(players->'${'$'}[*].cognitoUid', 'one', :cognitoUid)), '.saveAt'), :saveAt
        )
        where id = :practiceId
    """,
        nativeQuery = true,
    )
    fun updatePlayerStatusAndSaveAt(
        practiceId: String,
        cognitoUid: String,
        status: String?,
        saveAt: LocalDateTime,
    )

    @Modifying
    @Query(
        """
        update practice set players =
        json_set(players,
        CONCAT(JSON_UNQUOTE(JSON_SEARCH(players->'${'$'}[*].cognitoUid', 'one', :cognitoUid)), '.answers'), CAST(:answers as JSON)
        )
        where id = :practiceId
    """,
        nativeQuery = true,
    )
    fun updatePlayerAnswers(
        practiceId: String,
        cognitoUid: String,
        answers: String,
    )

    @Query(
        """
        select * from practice
        where :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        and mode not in ('CASUAL_TASK', 'CHALLENGE_TASK')
        and start_at between :startOfDate and :endOfDate
        """,
        nativeQuery = true,
    )
    fun findAllSelfPracticesByCognitoUidAndStartAtBetween(
        cognitoUid: String,
        startOfDate: LocalDateTime,
        endOfDate: LocalDateTime,
    ): List<PracticeEntity>

    @Query(
        """
        select * from practice
        where :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        and mode in ('CASUAL', 'CHALLENGE', 'BATTLE', 'ERROR_RELATED', 'ERROR_CHALLENGE')
        order by start_at desc limit :limit
        """,
        nativeQuery = true,
    )
    fun findAllSelfPracticesByCognitoUidOrderByStartAtLimit(
        cognitoUid: String,
        limit: Int,
    ): List<PracticeEntity>

    @Query(
        """
        SELECT * FROM practice
        WHERE :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        AND course_id = :courseId
        AND MODE IN ('ERROR_RELATED', 'ERROR_CHALLENGE')
        """,
        nativeQuery = true,
    )
    fun findAllErrorLogbookPracticesBy(
        cognitoUid: String,
        courseId: String,
    ): List<PracticeEntity>

    @Query(
        """
        SELECT count(*) FROM practice
        WHERE :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        AND course_id = :courseId
        AND MODE IN ('ERROR_RELATED', 'ERROR_CHALLENGE')
        """,
        nativeQuery = true,
    )
    fun findAllErrorLogbookPracticeCount(
        cognitoUid: String,
        courseId: String,
    ): Int

    @Query(
        """
        select * from practice
        where :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        and start_at > :createdAt
        """,
        nativeQuery = true,
    )
    fun findByCognitoUidAndCreatedAtAfter(
        cognitoUid: String,
        createdAt: LocalDateTime,
    ): List<PracticeEntity>

    @Query(
        """
        select * from practice
        where :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        and status = 'FINISHED'
        order by created_at desc limit :limit
        """,
        nativeQuery = true,
    )
    fun findFinishedPracticeByCognitoUidOrderByCreatedAtDesc(
        cognitoUid: String,
        limit: Int,
    ): List<PracticeEntity>

    @Query(
        """
        SELECT * FROM practice 
        WHERE :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid') 
        AND course_id = :courseId 
        AND status = 'FINISHED' 
        AND (:startOfDate IS NULL OR start_at BETWEEN :startOfDate AND :endOfDate ) 
        """,
        nativeQuery = true,
    )
    fun findAllSelfPracticesByCognitoUidAndCourseAndStartAtBetween(
        cognitoUid: String,
        courseId: String,
        startOfDate: LocalDateTime?,
        endOfDate: LocalDateTime,
    ): List<PracticeEntity>

    @Query(
        """
        SELECT * FROM practice p
        WHERE p.student_task_id = :studentTaskId
        AND :cognitoUid MEMBER OF(players->'${'$'}[*].cognitoUid')
        AND p.mode = :mode
        """,
        nativeQuery = true,
    )
    fun findAllByStudentTaskIdAndCognitoUidAndMode(
        studentTaskId: String,
        cognitoUid: String,
        mode: String,
    ): List<PracticeEntity>

    fun findByIdIn(ids: List<String>): List<PracticeEntity>

    fun findByStatusAndEndAtGreaterThanAndEndAtLessThan(
        status: PracticeStatus,
        practiceEndAt: LocalDateTime,
        historyStartAt: LocalDateTime?,
    ): List<PracticeEntity>
}

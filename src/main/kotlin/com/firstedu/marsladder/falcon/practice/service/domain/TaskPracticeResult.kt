package com.firstedu.marsladder.falcon.practice.service.domain

import com.firstedu.marsladder.falcon.practice.PracticeStatus
import com.firstedu.marsladder.falcon.practice.repository.entity.Answer
import com.firstedu.marsladder.falcon.practice.repository.entity.Question
import com.firstedu.marsladder.falcon.practice.repository.entity.QuestionOption
import com.firstedu.marsladder.falcon.question.QuestionCAS
import com.firstedu.marsladder.falcon.question.QuestionDifficulty
import hasImageTag
import java.time.LocalDateTime

data class TaskPracticeResult(
    val practiceId: String,
    val courseId: String,
    val cognitoUid: String,
    val totalSeconds: Long,
    val questions: List<TaskPracticeQuestion>,
    val status: PracticeStatus,
    val startAt: LocalDateTime,
)

data class TaskPracticeQuestion(
    val cas: QuestionCAS,
    val difficulty: QuestionDifficulty,
    val topic: String,
    val answerAnalysis: String? = null,
    val studentAnsweredOptionId: String? = null,
    val isStudentAnswerCorrect: Boolean = false,
    val options: List<TaskPracticeQuestionOption>,
    val questionId: String,
    val originalQuestionId: String,
    val originalQuestionVersion: Int,
    val like: Boolean?,
) {
    companion object {
        fun from(
            question: Question,
            likeOrDislike: Boolean?,
        ) = TaskPracticeQuestion(
            cas = question.cas,
            difficulty = question.difficulty,
            topic = question.topic,
            answerAnalysis = question.answerAnalysis,
            studentAnsweredOptionId = null,
            isStudentAnswerCorrect = false,
            options = question.options.map(TaskPracticeQuestionOption::from),
            questionId = question.id,
            originalQuestionId = question.originalQuestionId,
            originalQuestionVersion = question.originalQuestionVersion,
            like = likeOrDislike,
        )

        fun from(
            question: Question,
            answerOrNull: Answer?,
            likeOrDislike: Boolean?,
        ) = TaskPracticeQuestion(
            cas = question.cas,
            difficulty = question.difficulty,
            topic = question.topic,
            answerAnalysis = question.answerAnalysis,
            studentAnsweredOptionId = answerOrNull?.optionId,
            isStudentAnswerCorrect = answerOrNull?.correct ?: false,
            options = question.options.map(TaskPracticeQuestionOption::from),
            questionId = question.id,
            originalQuestionId = question.originalQuestionId,
            originalQuestionVersion = question.originalQuestionVersion,
            like = likeOrDislike,
        )
    }
}

data class TaskPracticeQuestionOption(
    val optionId: String,
    val value: String,
    val correct: Boolean,
) {
    companion object {
        fun from(option: QuestionOption) =
            TaskPracticeQuestionOption(
                optionId = option.id,
                value = option.value,
                correct = option.correct,
            )
    }
}

fun TaskPracticeQuestion.containsImage(): Boolean {
    return hasImageTag(topic) || options.map { option -> hasImageTag(option.value) }.contains(true)
}

package com.firstedu.marsladder.falcon.practice.controller

import com.firstedu.marsladder.falcon.practice.PracticeStatus
import com.firstedu.marsladder.falcon.practice.controller.dto.SavePracticeRobotAnswerRequest
import com.firstedu.marsladder.falcon.practice.service.PracticeAnswerService
import com.firstedu.marsladder.falcon.practice.service.PracticeService
import com.firstedu.marsladder.falcon.practice.service.domain.Practice
import com.firstedu.marsladder.falcon.practice.service.domain.PracticeAnswer
import com.firstedu.marsladder.falcon.practice.service.domain.existsPlayerByCognitoUid
import com.firstedu.marsladder.falcon.practice.service.exception.InvalidPracticeException
import com.firstedu.marsladder.falcon.practice.service.exception.NoPracticePermissionException
import org.slf4j.LoggerFactory
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController

@RestController
class PracticeRobotController(
    private val practiceAnswerService: PracticeAnswerService,
    private val practiceService: PracticeService,
) {
    private val logger = LoggerFactory.getLogger(PracticeRobotController::class.java)

    @PutMapping("practices/{practiceId}/robot-answers")
    fun saveAnswer(
        @PathVariable practiceId: String,
        @RequestBody request: SavePracticeRobotAnswerRequest,
    ) {
        val practice = practiceService.findPracticeById(practiceId)
        val robotCognitoUid = request.robotCognitoUid
        checkPlayer(practice, robotCognitoUid)
        checkRunningPractice(practice.status)

        practiceAnswerService.saveAnswer(
            practiceId,
            PracticeAnswer(
                cognitoUid = robotCognitoUid,
                questionId = request.questionId,
                optionId = request.optionId,
            ),
        )

        logger.info(
            "[Robot answered practice]: practiceId: {} robotCognitoUid: {} questionId: {}, optionId: {}",
            practiceId,
            robotCognitoUid,
            request.questionId,
            request.optionId,
        )

        if (practiceAnswerService.hasFinishedAllQuestions(practiceId, robotCognitoUid)) {
            practiceService.submitPractice(robotCognitoUid, practice)
            logger.info(
                "[Robot submitted practice]: practiceId: {} robotCognitoUid: {}",
                practiceId,
                robotCognitoUid,
            )
        }
    }

    private fun checkPlayer(
        practice: Practice,
        cognitoUid: String,
    ) {
        practice.existsPlayerByCognitoUid(cognitoUid)
            ?: throw NoPracticePermissionException("Player: $cognitoUid not in this practice")
    }

    private fun checkRunningPractice(status: PracticeStatus) {
        if (status != PracticeStatus.RUNNING) {
            throw InvalidPracticeException("Invalid practice status: $status")
        }
    }
}

package com.firstedu.marsladder.falcon.tutor.tutorprofile.presentation

import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherPermission
import com.firstedu.marsladder.falcon.tutor.tutorprofile.service.TutorProfile
import java.time.LocalDateTime

data class TutorProfileDto(
    val userId: String,
    val profileId: String,
    val firstName: String,
    val lastName: String,
    val phoneNumber: String,
    val institution: String,
    val avatar: String,
    val avatarUrl: String,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val teacherPermissions: List<TeacherPermission>,
) {
    companion object {
        fun from(
            tutorProfile: TutorProfile,
            teacherPermissions: List<TeacherPermission>,
        ): TutorProfileDto {
            return TutorProfileDto(
                userId = tutorProfile.userId,
                profileId = tutorProfile.profileId,
                firstName = tutorProfile.firstName,
                lastName = tutorProfile.lastName,
                phoneNumber = tutorProfile.phoneNumber,
                institution = tutorProfile.institution,
                avatar = tutorProfile.avatar,
                avatarUrl = tutorProfile.avatarUrl,
                createdAt = tutorProfile.createdAt,
                updatedAt = tutorProfile.updatedAt,
                teacherPermissions = teacherPermissions,
            )
        }
    }
}

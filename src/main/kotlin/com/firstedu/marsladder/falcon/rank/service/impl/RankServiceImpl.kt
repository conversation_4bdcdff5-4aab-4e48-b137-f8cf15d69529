package com.firstedu.marsladder.falcon.rank.service.impl

import com.firstedu.marsladder.falcon.rank.repository.RankRepository
import com.firstedu.marsladder.falcon.rank.repository.entity.RankEntity
import com.firstedu.marsladder.falcon.rank.repository.entity.toDomain
import com.firstedu.marsladder.falcon.rank.service.RankService
import com.firstedu.marsladder.falcon.rank.service.domain.Rank
import com.firstedu.marsladder.falcon.rank.service.exception.RankNotFoundException
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.stereotype.Service

@Service
class RankServiceImpl(private val rankRepository: RankRepository) : RankService {
    override fun getAll(): List<Rank> =
        rankRepository.findByOrderByRankLevelAsc().map {
            Rank(id = it.id, name = it.name!!, rankLevel = it.rankLevel!!, star = it.star)
        }

    override fun save(rank: Rank): Rank {
        val rankEntity =
            rankRepository.save(RankEntity(name = rank.name, rankLevel = rank.rankLevel, star = rank.star))
        return rankEntity.toDomain()
    }

    override fun update(rank: Rank): Rank {
        rankRepository.findById(rank.id!!)
            .orElseThrow { RankNotFoundException("Rank information not found. Id: ${rank.id}") }

        val rankEntity =
            rankRepository.save(
                RankEntity(
                    id = rank.id,
                    name = rank.name,
                    rankLevel = rank.rankLevel,
                    star = rank.star,
                ),
            )
        return rankEntity.toDomain()
    }

    override fun delete(id: String) {
        try {
            rankRepository.deleteById(id)
        } catch (emptyResultDataAccessException: EmptyResultDataAccessException) {
            throw RankNotFoundException("Rank information not found. Id: $id")
        }
    }

    override fun findRankByRankLevel(rankLevel: Int): Rank {
        val rankEntity =
            rankRepository.findByRankLevel(rankLevel)
                .orElseThrow { RankNotFoundException("Rank information not found. RankLevel: $rankLevel") }
        return rankEntity.toDomain()
    }
}

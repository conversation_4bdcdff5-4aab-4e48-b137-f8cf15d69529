package com.firstedu.marsladder.falcon.seatSubscription.repository

import com.firstedu.marsladder.falcon.seatSubscription.repository.entity.SeatSubscriptionEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

@Repository
interface SeatSubscriptionRepository :
    JpaRepository<SeatSubscriptionEntity, String>,
    JpaSpecificationExecutor<SeatSubscriptionEntity> {
    fun findByCognitoUidAndId(
        cognitoUid: String,
        id: String,
    ): SeatSubscriptionEntity?

    fun findByCognitoUid(cognitoUid: String): List<SeatSubscriptionEntity>

    fun findByCognitoUidAndStripeSubscriptionId(
        cognitoUid: String,
        stripeSubscriptionId: String,
    ): SeatSubscriptionEntity?
}

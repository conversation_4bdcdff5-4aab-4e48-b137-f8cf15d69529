package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFolderStructure

data class ClassroomFolderStructureResponse(
    val id: String?,
    val name: String,
    val isCurrentFolder: Boolean? = null,
    val childrenFolders: List<ClassroomFolderStructureResponse>,
) {
    companion object {
        fun from(classroomFolderStructure: ClassroomFolderStructure): ClassroomFolderStructureResponse =
            ClassroomFolderStructureResponse(
                id = classroomFolderStructure.id,
                name = classroomFolderStructure.name,
                isCurrentFolder = classroomFolderStructure.isCurrentFolder,
                childrenFolders = classroomFolderStructure.childrenFolders.map { ClassroomFolderStructureResponse.from(it) },
            )
    }
}

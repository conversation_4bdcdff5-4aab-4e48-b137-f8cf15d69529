package com.firstedu.marsladder.falcon.classroom.service.impl

import com.firstedu.marsladder.falcon.attendance.service.impl.DailyAttendanceServiceImpl
import com.firstedu.marsladder.falcon.classroom.AcademicLevel
import com.firstedu.marsladder.falcon.classroom.ClassroomStatus
import com.firstedu.marsladder.falcon.classroom.ClassroomStudentStatus
import com.firstedu.marsladder.falcon.classroom.ClassroomType
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomRepository
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomStudentDetailRepository
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomStudentRepository
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomEntity
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomStudentEntity
import com.firstedu.marsladder.falcon.classroom.service.ClassroomStudentService
import com.firstedu.marsladder.falcon.classroom.service.domain.AddStudentResult
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomStudent
import com.firstedu.marsladder.falcon.classroom.service.exception.AddStudentNoPermissionException
import com.firstedu.marsladder.falcon.classroom.service.exception.ClassroomNotFoundException
import com.firstedu.marsladder.falcon.classroom.service.exception.ClassroomStudentNotFoundException
import com.firstedu.marsladder.falcon.classroom.service.exception.DuplicateEmailException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidClassroomStudentEntryExistTimesException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidStudentAliasException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidTeacherException
import com.firstedu.marsladder.falcon.classroom.service.exception.UpdateDismissedClassroomException
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.constant.RegexConstants
import com.firstedu.marsladder.falcon.marketing.event.MarketingEvent
import com.firstedu.marsladder.falcon.marketing.event.MarketingEventPublisher
import com.firstedu.marsladder.falcon.marketing.event.MarketingEventType
import com.firstedu.marsladder.falcon.notification.NotificationFirstLevelType
import com.firstedu.marsladder.falcon.notification.NotificationSecondLevelType
import com.firstedu.marsladder.falcon.notification.NotificationStatus
import com.firstedu.marsladder.falcon.notification.repository.NotificationRepository
import com.firstedu.marsladder.falcon.notification.repository.entity.NotificationEntity
import com.firstedu.marsladder.falcon.profile.repository.ProfileRepository
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherPermission.ADD_ANY_EMAIL_TO_CLASSROOM
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherPermissionService
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherProfileService
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.tutor.tutorprofile.service.TutorProfileService
import com.firstedu.marsladder.falcon.tutor1.service.SeatService
import com.firstedu.marsladder.falcon.user.consumer.ConsumerService
import com.firstedu.marsladder.falcon.user.consumer.ConsumerType
import com.firstedu.marsladder.falcon.user.identity.repository.UserRepository
import com.firstedu.marsladder.falcon.utils.DateTimeUtil
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class ClassroomStudentServiceImpl(
    private val consumerService: ConsumerService,
    private val teacherProfileService: TeacherProfileService,
    private val tutorProfileService: TutorProfileService,
    private val classroomStudentRepository: ClassroomStudentRepository,
    private val classroomRepository: ClassroomRepository,
    private val notificationRepository: NotificationRepository,
    private val userRepository: UserRepository,
    private val profileRepository: ProfileRepository,
    private val sessionProvider: SessionProvider,
    private val seatService: SeatService,
    private val marketingEventPublisher: MarketingEventPublisher,
    private val classroomStudentDetailRepository: ClassroomStudentDetailRepository,
    private val s3Properties: S3Properties,
    private val dateTimeUtil: DateTimeUtil,
    private val teacherPermissionService: TeacherPermissionService,
) : ClassroomStudentService {
    companion object {
        private val logger = LoggerFactory.getLogger(DailyAttendanceServiceImpl::class.java)
    }

    override fun getStudents(classroomId: String): List<ClassroomStudent> {
        return classroomStudentDetailRepository
            .findByClassroomIdAndStatus(classroomId, ClassroomStudentStatus.IN)
            .map { ClassroomStudent.from(it, s3Properties.static.url, s3Properties.avatar.path, s3Properties.avatar.default) }
            .sortedWith(compareByDescending<ClassroomStudent> { it.createdAt }.thenBy { it.studentAlias })
    }

    override fun getStudent(
        classroomId: String,
        studentId: String,
    ): ClassroomStudent {
        return classroomStudentDetailRepository
            .findByClassroomIdAndStudentCognitoUid(classroomId, studentId)
            .map { ClassroomStudent.from(it, s3Properties.static.url, s3Properties.avatar.path, s3Properties.avatar.default) }
            .orElseThrow { ClassroomStudentNotFoundException("Student $studentId not found in classroom $classroomId") }
    }

    override fun getStudentsSuggestions(classroomId: String): List<ClassroomStudent> {
        return classroomStudentRepository.findByClassroomIdAndStatus(classroomId, ClassroomStudentStatus.IN)
            .map {
                val profileOptional = profileRepository.findByCognitoUid(it.studentCognitoUid)
                ClassroomStudent.from(it).apply {
                    this.studentAvatarUrl = "${s3Properties.static.url}/${s3Properties.avatar.path}/${
                        profileOptional.map { profile -> profile.avatar }.orElse(s3Properties.avatar.default)
                    }"
                }
            }
    }

    private fun checkAddStudentPermission(
        userId: String,
        classroomEntity: ClassroomEntity,
        emails: List<String>,
    ) {
        when (classroomEntity.type) {
            ClassroomType.TEACHER -> {
                if (!teacherProfileService.isProfileExisted(userId)) {
                    throw AddStudentNoPermissionException("User $userId can not operate classroom: ${classroomEntity.id}")
                }
            }
            ClassroomType.TUTOR -> {
                if (!tutorProfileService.isProfileExisted(userId)) {
                    throw AddStudentNoPermissionException("User $userId can not operate classroom: ${classroomEntity.id}")
                }
                val seatInfo = seatService.findSeatsByTutor(userId)
                val used = seatInfo.used
                val currentCapacity = seatInfo.capacity.current?.capacity ?: 0

                if (used + emails.size > currentCapacity) {
                    throw AddStudentNoPermissionException("User $userId does not have enough seat on classroom: ${classroomEntity.id}")
                }
            }
        }
    }

    override fun addStudents(
        userId: String,
        classroomId: String,
        emails: List<String>,
    ): AddStudentResult {
        val classroomEntity = validateClassroom(classroomId)

        checkAddStudentPermission(userId, classroomEntity, emails)

        validateEmailDuplicates(emails)

        val teacherEmail = sessionProvider.getEmail() ?: throw InvalidTeacherException("Invalid teacher email")
        val classroomStudentEntities = ArrayList<ClassroomStudentEntity>()
        val failedEmails = ArrayList<String>()
        val existedEmails = ArrayList<String>()
        for (email in emails) {
            if (StringUtils.equals(teacherEmail, email)) {
                failedEmails.add(email)
                continue
            }
            if (!allowToAdd(email, sessionProvider.getUserId())) {
                failedEmails.add(email)
                continue
            }

            var cognitoUid = userRepository.findFirstByEmail(email)?.id
            if (cognitoUid == null) {
                val name = email.substringBefore("@")
                try {
                    cognitoUid = consumerService.createConsumer(name, email, ConsumerType.CLASS_STUDENT)
                } catch (e: Exception) {
                    logger.error("Failed to create student", e)
                    failedEmails.add(email)
                    continue
                }
            }
            val classroomStudentOptional =
                classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(classroomId, cognitoUid)
            if (classroomStudentOptional.isEmpty) {
                classroomStudentEntities.add(
                    ClassroomStudentEntity(
                        classroomId = classroomId,
                        studentCognitoUid = cognitoUid,
                        status = ClassroomStudentStatus.IN,
                        academicLevel = AcademicLevel.Exploring,
                        entryExitTimes = mutableListOf(dateTimeUtil.getNowTime()),
                    ),
                )
            } else {
                val classroomStudentEntity = classroomStudentOptional.get()
                if (classroomStudentEntity.status == ClassroomStudentStatus.OUT) {
                    classroomStudentEntity.status = ClassroomStudentStatus.IN
                    classroomStudentEntity.entryExitTimes.addLast(dateTimeUtil.getNowTime())
                    classroomStudentEntities.add(classroomStudentEntity)
                } else {
                    existedEmails.add(email)
                }
            }
        }

        if (classroomStudentEntities.size > 0) {
            classroomStudentRepository.saveAll(classroomStudentEntities)
            classroomStudentEntities.forEach {
                notifyStudent(
                    it.studentCognitoUid,
                    "You have been added to class ${classroomEntity.name}. <a href=\"/classroom/${it.classroomId}/details\">Click this</a> to get more details!",
                )
            }
        }

        val addStudentResult = AddStudentResult(classroomStudentEntities.size, failedEmails, existedEmails)
        marketingEventPublisher.publish(
            MarketingEvent(userId = userId, MarketingEventType.INVITE_STUDENTS, BigDecimal.valueOf(addStudentResult.successCount.toLong())),
        )
        return addStudentResult
    }

    private fun allowToAdd(
        email: String,
        userId: String,
    ): Boolean {
        return when {
            tutorProfileService.isProfileExisted(userId) -> true
            teacherPermissionService.getTeacherPermissions(userId).contains(ADD_ANY_EMAIL_TO_CLASSROOM) -> true
            else -> Regex(RegexConstants.REGEX_STUDENT_EMAIL).matches(email)
        }
    }

    private fun validateEmailDuplicates(emails: List<String>) {
        val normalizedEmails = emails.map { it.lowercase() }
        val duplicates = normalizedEmails.groupingBy { it }.eachCount().filter { it.value > 1 }.keys

        if (duplicates.isNotEmpty()) {
            val originalDuplicates =
                emails.filter { email ->
                    duplicates.contains(email.lowercase())
                }
            throw DuplicateEmailException("Duplicate emails found (case-insensitive): ${originalDuplicates.joinToString(", ")}")
        }
    }

    override fun moveOutStudent(
        classroomId: String,
        classroomStudentId: String,
    ): ClassroomStudent {
        val classroomEntity = validateClassroom(classroomId)

        return classroomStudentRepository.findById(classroomStudentId)
            .map {
                it.status = ClassroomStudentStatus.OUT
                it.entryExitTimes.addLast(dateTimeUtil.getNowTime())
                val savedEntity = classroomStudentRepository.save(it)
                notifyStudent(
                    savedEntity.studentCognitoUid,
                    "You have been removed from class \"${classroomEntity.name}\"!",
                )
                ClassroomStudent.from(savedEntity)
            }
            .orElseThrow { ClassroomStudentNotFoundException("Student not found, id: $classroomStudentId") }
    }

    override fun updateStudentAlias(
        classroomId: String,
        classroomStudentId: String,
        studentAlias: String,
    ): ClassroomStudent {
        validateAlias(studentAlias)
        validateClassroom(classroomId)

        return classroomStudentRepository.findById(classroomStudentId)
            .map {
                it.studentAlias = studentAlias
                val savedEntity = classroomStudentRepository.save(it)
                ClassroomStudent.from(savedEntity)
            }
            .orElseThrow { ClassroomStudentNotFoundException("Student not found, id: $classroomStudentId") }
    }

    private fun validateAlias(studentAlias: String) {
        if (StringUtils.isBlank(studentAlias) || studentAlias.length > 100) {
            throw InvalidStudentAliasException("Student alias should between 1 to 100 letters")
        }
    }

    override fun updateAcademicLevel(
        classroomId: String,
        classroomStudentCognitoUid: String,
        academicLevel: AcademicLevel,
    ): ClassroomStudent {
        validateClassroom(classroomId)

        return classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(classroomId, classroomStudentCognitoUid)
            .map {
                it.academicLevel = academicLevel
                val savedEntity = classroomStudentRepository.save(it)
                ClassroomStudent.from(savedEntity)
            }
            .orElseThrow { ClassroomStudentNotFoundException("Student not found, id: $classroomStudentCognitoUid") }
    }

    override fun getInvitedStudentCount(userId: String): Int {
        val classroomIds = classroomRepository.findByTeacherCognitoUid(userId).map { it.id!! }
        return classroomStudentRepository.countByClassroomIdIn(classroomIds)
    }

    override fun isStudentInClassTimeRange(
        currentTime: LocalDateTime,
        studentUserId: String,
        classroomId: String,
    ): Boolean {
        val entryExitTimes =
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(classroomId, studentUserId)
                .map {
                    it.entryExitTimes
                }.orElseThrow { ClassroomStudentNotFoundException("Classroom: $classroomId student: $studentUserId not found.") }

        if (entryExitTimes.isEmpty()) {
            throw InvalidClassroomStudentEntryExistTimesException("Student $studentUserId has no entry/exit records in classroom $classroomId.")
        }

        return entryExitTimes.chunked(2)
            .map {
                if (it.size == 1) it + null else it
            }
            .any { (entryTime, exitTime) -> isWithinTimeRange(currentTime, entryTime!!, exitTime) }
    }

    private fun isWithinTimeRange(
        currentTime: LocalDateTime,
        entryTime: LocalDateTime,
        exitTime: LocalDateTime?,
    ): Boolean {
        return exitTime?.let { currentTime.isAfter(entryTime) && currentTime.isBefore(exitTime) }
            ?: currentTime.isAfter(entryTime)
    }

    private fun validateClassroom(classroomId: String): ClassroomEntity {
        val classroomEntity =
            classroomRepository.findById(classroomId)
                .orElseThrow { ClassroomNotFoundException("Classroom not found, id: $classroomId") }

        classroomEntity.takeIf { it.status == ClassroomStatus.DISMISSED }
            ?.apply { throw UpdateDismissedClassroomException("Class ${this.id} was dismissed") }

        return classroomEntity
    }

    private fun notifyStudent(
        studentCognitoUid: String,
        message: String,
    ) {
        notificationRepository.save(
            NotificationEntity(
                null,
                message,
                studentCognitoUid,
                NotificationStatus.NEW,
                NotificationFirstLevelType.CLASS,
                NotificationSecondLevelType.CLASS,
                dateTimeUtil.getNowTime(),
            ),
        )
    }
}

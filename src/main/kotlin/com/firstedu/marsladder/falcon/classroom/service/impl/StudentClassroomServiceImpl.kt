package com.firstedu.marsladder.falcon.classroom.service.impl

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.ClassroomStudentStatus
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomFileRepository
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomLectureRepository
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomRepository
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomStudentRepository
import com.firstedu.marsladder.falcon.classroom.service.ClassroomLectureState
import com.firstedu.marsladder.falcon.classroom.service.ClassroomStudentService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomTeacherService
import com.firstedu.marsladder.falcon.classroom.service.StudentClassroomService
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomLecture
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomStudent
import com.firstedu.marsladder.falcon.classroom.service.domain.StudentClassroom
import com.firstedu.marsladder.falcon.classroom.service.exception.ClassroomNotFoundException
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.profile.repository.ProfileRepository
import org.apache.commons.compress.utils.Lists
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service

@Service
class StudentClassroomServiceImpl(
    private val classroomRepository: ClassroomRepository,
    private val classroomStudentRepository: ClassroomStudentRepository,
    private val classroomTeacherService: ClassroomTeacherService,
    private val profileRepository: ProfileRepository,
    private val s3Properties: S3Properties,
    private val classroomFileRepository: ClassroomFileRepository,
    private val classroomLectureRepository: ClassroomLectureRepository,
    private val classroomStudentService: ClassroomStudentService,
) : StudentClassroomService {
    override fun getStudentClassrooms(
        top: Int?,
        studentUserId: String,
    ): List<StudentClassroom> {
        val classroomStudentEntityList =
            top?.let {
                classroomStudentRepository
                    .findByStudentCognitoUidOrderByCreatedAtDesc(studentUserId, PageRequest.of(0, it))
            }
                ?: classroomStudentRepository
                    .findByStudentCognitoUidOrderByCreatedAtDesc(studentUserId)
        val studentClassrooms =
            classroomStudentEntityList
                .map { classroomRepository.findById(it.classroomId).orElseThrow { ClassroomNotFoundException("Classroom ${it.classroomId} not found.") } }
                .map { classroomEntity ->
                    val studentList =
                        classroomStudentRepository.findByClassroomIdAndStatus(
                            classroomEntity.id!!,
                            ClassroomStudentStatus.IN,
                        )
                    StudentClassroom.from(classroomEntity, studentList, classroomStudentEntityList.find { it.classroomId == classroomEntity.id!! }!!.status)
                }

        return studentClassrooms
    }

    override fun getStudentClassroom(
        classroomId: String,
        studentUserId: String,
    ): StudentClassroom {
        val classroomStudentEntity = classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(classroomId, studentUserId).orElseThrow { ClassroomNotFoundException("Classroom $classroomId not found.") }
        return classroomRepository.findById(classroomStudentEntity.classroomId)
            .map { classroomEntity ->
                val studentList =
                    classroomStudentRepository.findByClassroomIdAndStatus(
                        classroomEntity.id!!,
                        ClassroomStudentStatus.IN,
                    )
                val classroomRootFolder = getRootFolderByClassroom(classroomId)
                StudentClassroom.from(classroomEntity, studentList, classroomStudentEntity.status, classroomRootFolder)
            }
            .orElseThrow { ClassroomNotFoundException("Classroom ${classroomStudentEntity.classroomId} not found.") }
    }

    override fun getClassmates(
        classroomId: String,
        studentUserId: String,
    ): List<ClassroomStudent> {
        return classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(classroomId, studentUserId)
            .map {
                classroomStudentRepository.findByClassroomIdAndStatus(
                    classroomId,
                    ClassroomStudentStatus.IN,
                ).filter { it.studentCognitoUid != studentUserId }
                    .map {
                        val profileOptional = profileRepository.findByCognitoUid(it.studentCognitoUid)
                        val level = profileOptional.map { profile -> profile.level }.orElse(1)
                        val avatar = profileOptional.map { profile -> profile.avatar }.orElse(s3Properties.avatar.default)
                        if (it.studentAlias == null) {
                            val nickname = profileOptional.map { profile -> profile.nickname }.orElse("")
                            it.studentAlias = nickname
                        }
                        ClassroomStudent.from(it, level, avatar)
                    }
            }
            .orElseThrow { ClassroomNotFoundException("Classroom not found, id: $classroomId") }
    }

    override fun getChildrenFilesByFolderId(
        classroomId: String,
        folderId: String,
        studentUserId: String,
    ): List<ClassroomFile> {
        classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, folderId, true)?.let {
            return Lists.newArrayList()
        }

        val classroomFileEntityList =
            classroomFileRepository.findByClassroomIdAndParentIdAndStateAndDeleted(classroomId, folderId, ClassroomFileState.PUBLISHED, false)
                .filterNot {
                    it.type == ClassroomFileType.FILE && !classroomStudentService.isStudentInClassTimeRange(it.publishedAt!!, studentUserId, classroomId)
                }

        return classroomFileEntityList.map { classroomFileEntity ->
            val uploadedByName = classroomTeacherService.getClassroomTeacherOrTutorName(classroomFileEntity.classroomId, classroomFileEntity.uploadedBy)
            ClassroomFile.from(classroomFileEntity, uploadedByName)
        }
    }

    override fun getLecturesByClassroomId(
        classroomId: String,
        studentUserId: String,
    ): List<ClassroomLecture> {
        val classroomLectureEntityList =
            classroomLectureRepository.findByClassroomIdAndStateAndDeleted(classroomId, ClassroomLectureState.PUBLISHED, false)
                .filter {
                    classroomStudentService.isStudentInClassTimeRange(it.publishedAt!!, studentUserId, classroomId)
                }
        return classroomLectureEntityList.map { classroomLectureEntity ->
            val uploadedByName = classroomTeacherService.getClassroomTeacherOrTutorName(classroomLectureEntity.classroomId, classroomLectureEntity.uploadedBy)
            ClassroomLecture.from(classroomLectureEntity, uploadedByName)
        }
    }

    override fun hasPermissionToReadClassroom(
        classroomId: String,
        operatorUserId: String,
    ): Boolean {
        return classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(classroomId, operatorUserId).isPresent
    }

    private fun getRootFolderByClassroom(classroomId: String): ClassroomFile? {
        val classroomFiles = classroomFileRepository.findByClassroomIdAndRootFolder(classroomId, true)
        return classroomFiles.firstOrNull()?.let { ClassroomFile.from(it) }
    }
}

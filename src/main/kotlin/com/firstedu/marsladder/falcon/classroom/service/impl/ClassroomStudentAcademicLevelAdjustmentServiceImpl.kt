package com.firstedu.marsladder.falcon.classroom.service.impl

import com.firstedu.marsladder.falcon.classroom.AcademicLevel
import com.firstedu.marsladder.falcon.classroom.event.AcademicLevelAdjustedEvent
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomStudentAcademicLevelAdjustmentRepository
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomStudentAcademicLevelAdjustmentEntity
import com.firstedu.marsladder.falcon.classroom.service.ClassroomStudentAcademicLevelAdjustmentService
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomStudentAcademicLevelAdjustment
import com.firstedu.marsladder.falcon.principal.service.domain.AcademicLevelStatistic
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.event.TransactionalEventListener
import java.time.Duration
import java.time.LocalDateTime

@Service
class ClassroomStudentAcademicLevelAdjustmentServiceImpl(
    private val academicLevelAdjustmentRepository: ClassroomStudentAcademicLevelAdjustmentRepository,
) : ClassroomStudentAcademicLevelAdjustmentService {
    companion object {
        private val logger = LoggerFactory.getLogger(ClassroomStudentAcademicLevelAdjustmentServiceImpl::class.java)
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    override fun handleClassStudentAcademicLevelAdjustedEvent(academicLevelAdjustedEvent: AcademicLevelAdjustedEvent) {
        val adjustment = academicLevelAdjustedEvent.academicLevelAdjustment
        logger.info(
            "student: {} in classroom: {} , academic_level: {} suggest_academic_level : {}",
            adjustment.studentUserId,
            adjustment.classroomId,
            adjustment.academicLevel,
            adjustment.suggestAcademicLevel,
        )

        if (isAcademicLevelFieldChanged(adjustment)) {
            academicLevelAdjustmentRepository.save(adjustment.toEntity())
        }
    }

    override fun getAcademicLevelStatisticByClassroomId(
        classroomId: String,
        targetTime: LocalDateTime,
    ): AcademicLevelStatistic {
        val result =
            academicLevelAdjustmentRepository.findByClassroomId(classroomId)
                .groupBy { it.studentUserId }
                .values
                .mapNotNull { getLatestEntity(it, targetTime) }
                .map { it.suggestAcademicLevel ?: it.academicLevel }

        val levelCountMap = result.groupingBy { it }.eachCount()
        return AcademicLevelStatistic(
            exploringLevelCount = levelCountMap[AcademicLevel.Exploring] ?: 0,
            goodLevelCount = levelCountMap[AcademicLevel.Good] ?: 0,
            excellentLevelCount = levelCountMap[AcademicLevel.Excellent] ?: 0,
        )
    }

    override fun getEarliestAcademicLevelChangeTimeByClassroomIds(
        classroomIds: List<String>,
    ): LocalDateTime? {
        return academicLevelAdjustmentRepository.findByClassroomIdIn(classroomIds)
            .minByOrNull { it.createdAt!! }
            ?.createdAt
    }

    private fun getLatestEntity(
        entities: List<ClassroomStudentAcademicLevelAdjustmentEntity>,
        targetTime: LocalDateTime,
    ): ClassroomStudentAcademicLevelAdjustmentEntity? {
        return entities.minByOrNull { Duration.between(targetTime, it.createdAt).abs() }
    }

    private fun isAcademicLevelFieldChanged(
        academicLevelAdjustment: ClassroomStudentAcademicLevelAdjustment,
    ): Boolean {
        val entityList =
            academicLevelAdjustmentRepository.findByClassroomIdAndStudentUserIdOrderByCreatedAtDesc(
                academicLevelAdjustment.classroomId,
                academicLevelAdjustment.studentUserId,
            )

        val (changedAcademicLevel, changedSuggestAcademicLevel) =
            academicLevelAdjustment.run {
                academicLevel to suggestAcademicLevel
            }

        if (entityList.isEmpty()) return true

        val latestAdjustment = entityList.first()
        return latestAdjustment.academicLevel != changedAcademicLevel || latestAdjustment.suggestAcademicLevel != changedSuggestAcademicLevel
    }
}

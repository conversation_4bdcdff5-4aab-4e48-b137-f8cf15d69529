package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import java.time.ZoneOffset

data class ClassroomFileResponse(
    val folderPath: List<FileResponse>,
    val childrenFiles: List<FileResponse>,
) {
    companion object {
        fun from(
            folderPath: List<ClassroomFile>,
            childrenFiles: List<ClassroomFile>,
        ): ClassroomFileResponse =
            ClassroomFileResponse(
                folderPath = folderPath.map { FileResponse.from(it) },
                childrenFiles = childrenFiles.map { FileResponse.from(it) },
            )
    }
}

data class FileResponse(
    val id: String? = null,
    val s3FileId: String? = null,
    val fileExtension: String? = null,
    val name: String,
    val state: ClassroomFileState,
    val type: ClassroomFileType,
    val uploadedBy: String? = null,
    val publishedAt: Long?,
    val uploadedAt: Long,
) {
    companion object {
        fun from(classroomFile: ClassroomFile) =
            FileResponse(
                id = classroomFile.id,
                s3FileId = classroomFile.s3File.takeIf { classroomFile.type == ClassroomFileType.FILE }?.id,
                fileExtension = classroomFile.s3File.takeIf { classroomFile.type == ClassroomFileType.FILE }?.extension,
                name = classroomFile.name,
                state = classroomFile.state,
                type = classroomFile.type,
                uploadedBy = classroomFile.uploadedBy,
                publishedAt = classroomFile.publishedAt?.toEpochSecond(ZoneOffset.UTC),
                uploadedAt = classroomFile.createdAt!!.toEpochSecond(ZoneOffset.UTC),
            )
    }
}

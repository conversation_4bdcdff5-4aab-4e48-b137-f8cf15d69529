package com.firstedu.marsladder.falcon.classroom.controller

import com.firstedu.marsladder.falcon.classroom.ClassroomStatus
import com.firstedu.marsladder.falcon.classroom.ClassroomTeacherType
import com.firstedu.marsladder.falcon.classroom.controller.dto.ClassroomResponse
import com.firstedu.marsladder.falcon.classroom.controller.dto.ClassroomSummaryResponse
import com.firstedu.marsladder.falcon.classroom.controller.dto.CreateClassroomRequest
import com.firstedu.marsladder.falcon.classroom.controller.dto.UpdateClassroomRequest
import com.firstedu.marsladder.falcon.classroom.controller.dto.filterXss
import com.firstedu.marsladder.falcon.classroom.service.ClassroomFileService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomTeacherService
import com.firstedu.marsladder.falcon.classroom.service.domain.Classroom
import com.firstedu.marsladder.falcon.client.nezha.NezhaClient
import com.firstedu.marsladder.falcon.course.service.SubjectService
import com.firstedu.marsladder.falcon.course.service.domain.SubjectType
import com.firstedu.marsladder.falcon.exception.NoPermissionException
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.task.service.TaskService
import com.firstedu.marsladder.falcon.task.service.TaskStatisticsService
import com.firstedu.marsladder.falcon.utils.XssFilter
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.YearMonth

@PreAuthorize("hasAnyRole('TEACHER','TUTOR')")
@RestController
class ClassroomController(
    private val sessionProvider: SessionProvider,
    private val classroomService: ClassroomService,
    private val taskStatisticsService: TaskStatisticsService,
    private val taskService: TaskService,
    private val xssFilter: XssFilter,
    private val classroomFileService: ClassroomFileService,
    private val classroomTeacherService: ClassroomTeacherService,
    private val nezhaClient: NezhaClient,
    private val subjectService: SubjectService,
) {
    private val logger = LoggerFactory.getLogger(ClassroomController::class.java)

    @PostMapping("/classrooms")
    fun createClassroom(
        @RequestBody createClassroomRequest: CreateClassroomRequest,
    ): ResponseEntity<ClassroomResponse> {
        val request = createClassroomRequest.filterXss(xssFilter)
        val classroom =
            classroomService.saveClassroom(
                Classroom(
                    name = request.name,
                    subjectId = request.subjectId,
                    courseId = request.courseId,
                    status = ClassroomStatus.IN_PROGRESS,
                    label = request.label,
                    teacherUserId = sessionProvider.getUserId(),
                    description = request.description,
                    type = request.type,
                    schoolCampusId = request.schoolCampusId,
                ),
            )
        classroomFileService.initializeClassroomRootFolder(classroom.id!!)
        classroomTeacherService.initializeClassroomTeacher(classroom.id, sessionProvider.getUserId())
        if (subjectService.getSubjectById(classroom.subjectId).name != SubjectType.Mathematics) {
            nezhaClient.initializeMultiSubjectCourseClassroomContent(classroom.id, classroom.courseId, classroom.subjectId)
        }
        return ResponseEntity(ClassroomResponse.from(classroom), HttpStatus.CREATED)
    }

    @PutMapping("/classrooms/{classroomId}")
    fun updateClassroom(
        @PathVariable classroomId: String,
        @RequestBody updateClassroomRequest: UpdateClassroomRequest,
    ): ResponseEntity<ClassroomResponse> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId(), listOf(ClassroomTeacherType.TEACHER))
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        val request = updateClassroomRequest.filterXss(xssFilter)
        val classroom =
            classroomService.saveClassroom(
                Classroom(
                    id = classroomId,
                    name = request.name,
                    subjectId = request.subjectId,
                    courseId = request.courseId,
                    status = request.status,
                    label = request.label,
                    teacherUserId = sessionProvider.getUserId(),
                    description = request.description,
                    schoolCampusId = request.schoolCampusId,
                ),
            )
        return ResponseEntity.ok(ClassroomResponse.from(classroom))
    }

    @DeleteMapping("/classrooms/{classroomId}")
    fun deleteClassroom(
        @PathVariable classroomId: String,
    ): ResponseEntity<Unit> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId(), listOf(ClassroomTeacherType.TEACHER))
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        val classroom = classroomService.getClassroomById(classroomId)
        classroomService.saveClassroom(
            Classroom(
                id = classroomId,
                name = classroom.name,
                subjectId = classroom.subjectId,
                courseId = classroom.courseId,
                status = ClassroomStatus.DISMISSED,
                label = classroom.label,
                teacherUserId = classroom.teacherUserId,
                description = classroom.description,
            ),
        )
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }

    @GetMapping("/classrooms")
    fun getClassrooms(
        @RequestParam(required = false) status: ClassroomStatus?,
        @RequestParam(required = false) courseId: String?,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") creationMonth: YearMonth?,
    ): ResponseEntity<List<ClassroomSummaryResponse>> {
        val classrooms =
            classroomService.getClassrooms(sessionProvider.getUserId(), status, courseId, creationMonth)
        val classroomIds = classrooms.map { it.id!! }
        val inProgressTasksCount = taskService.batchGetInProgressTaskCount(classroomIds)

        return ResponseEntity.ok(
            classrooms.map { classroom ->
                ClassroomSummaryResponse.from(
                    classroom,
                    inProgressTasksCount[classroom.id],
                    subjectService.getSubjectById(classroom.subjectId).name,
                )
            },
        )
    }

    @GetMapping("/classrooms/{classroomId}")
    fun getClassroom(
        @PathVariable classroomId: String,
    ): ResponseEntity<ClassroomResponse> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        return ResponseEntity.ok(getClassroomResponse(classroomId, false))
    }

    @PreAuthorize("hasAnyRole('REALUS_PARENT', 'REALUS_STUDENT')")
    @GetMapping("/students/{studentId}/classrooms")
    fun getClassroomResponses(
        @PathVariable studentId: String,
        @RequestParam classroomIds: List<String>?,
    ): ResponseEntity<List<ClassroomResponse>> {
        if (classroomIds.isNullOrEmpty()) return ResponseEntity.ok(emptyList())
        val userId = sessionProvider.getUserId()
        val validClassroomIds =
            classroomIds.filter { classroomId ->
                if (studentId == userId) {
                    classroomService.hasPermissionForRealusStudentToReadClassroom(classroomId, studentId)
                } else {
                    classroomService.hasPermissionForRealusParentToReadClassroom(classroomId, userId, studentId)
                }
            }

        val classroomResponse = validClassroomIds.map { classroomId -> getClassroomResponse(classroomId) }
        return ResponseEntity.ok(classroomResponse)
    }

    private fun getClassroomResponse(
        classroomId: String,
        filterOnTime: Boolean = true,
    ): ClassroomResponse {
        val classroom = classroomService.getClassroomById(classroomId)
        val classroomStatistics = taskStatisticsService.getClassroomStatisticsByClassroomId(classroomId, filterOnTime)
        val averageAccuracy = if (classroomStatistics.totalQuestionCount != 0) classroomStatistics.correctQuestionCount.toDouble() / classroomStatistics.totalQuestionCount else 0.0
        val averageCompletionRate = if (classroomStatistics.totalSetCount != 0) classroomStatistics.completedSetCount.toDouble() / classroomStatistics.totalSetCount else 0.0
        return ClassroomResponse.from(classroom, averageAccuracy, averageCompletionRate)
    }

    @PutMapping("/classrooms/{classroomId}/notification-status")
    fun updateEmptyNotificationStatus(
        @PathVariable classroomId: String,
    ): ResponseEntity<Unit> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        classroomService.updateEmptyNotificationStatus(classroomId)
        return ResponseEntity.noContent().build()
    }
}

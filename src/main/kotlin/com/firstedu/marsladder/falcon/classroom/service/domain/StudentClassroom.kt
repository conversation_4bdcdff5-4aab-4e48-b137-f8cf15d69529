package com.firstedu.marsladder.falcon.classroom.service.domain

import com.firstedu.marsladder.falcon.classroom.ClassroomStatus
import com.firstedu.marsladder.falcon.classroom.ClassroomStudentStatus
import com.firstedu.marsladder.falcon.classroom.ClassroomType
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomEntity
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomStudentEntity
import java.time.LocalDateTime

data class StudentClassroom(
    val id: String? = null,
    val name: String,
    val subjectId: String,
    val courseId: String,
    val status: ClassroomStatus,
    val studentStatus: ClassroomStudentStatus,
    val label: String,
    val teacherUserId: String,
    val description: String? = null,
    val emptyNotified: Boolean? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
    val studentList: List<ClassroomStudent>? = null,
    var type: ClassroomType? = ClassroomType.TEACHER,
    var rootFolderId: String? = null,
) {
    companion object {
        fun from(
            classroomEntity: ClassroomEntity,
            studentList: List<ClassroomStudentEntity>,
            studentStatus: ClassroomStudentStatus,
            classroomRootFolder: ClassroomFile? = null,
        ) = StudentClassroom(
            id = classroomEntity.id,
            name = classroomEntity.name,
            subjectId = classroomEntity.subjectId,
            courseId = classroomEntity.courseId,
            status = classroomEntity.status,
            studentStatus = studentStatus,
            label = classroomEntity.label,
            teacherUserId = classroomEntity.teacherCognitoUid,
            description = classroomEntity.description,
            emptyNotified = classroomEntity.emptyNotified,
            createdAt = classroomEntity.createdAt,
            updatedAt = classroomEntity.updatedAt,
            studentList = studentList.map(ClassroomStudent::from),
            type = classroomEntity.type,
            rootFolderId = classroomRootFolder?.id,
        )
    }
}

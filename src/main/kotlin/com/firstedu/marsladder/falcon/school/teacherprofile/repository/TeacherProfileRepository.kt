package com.firstedu.marsladder.falcon.school.teacherprofile.repository

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TeacherProfileRepository : JpaRepository<TeacherProfileRepositoryEntity, String> {
    fun findTopByUserId(userId: String): TeacherProfileRepositoryEntity?

    fun findBySchoolId(schoolId: String): List<TeacherProfileRepositoryEntity>

    fun existsByUserId(userId: String): Boolean

    fun deleteByUserId(userId: String): Int
}

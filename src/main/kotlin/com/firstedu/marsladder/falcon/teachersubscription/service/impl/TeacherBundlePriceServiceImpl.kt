package com.firstedu.marsladder.falcon.teachersubscription.service.impl

import com.firstedu.marsladder.falcon.subscription.type.SubscriptionFrequency
import com.firstedu.marsladder.falcon.teachersubscription.config.TeacherBundleSubscriptionProperties
import com.firstedu.marsladder.falcon.teachersubscription.service.TeacherBundlePriceService
import com.firstedu.marsladder.falcon.teachersubscription.service.domain.TeacherBundlePrice
import org.springframework.stereotype.Service

@Service
class TeacherBundlePriceServiceImpl(
    private val teacherBundleSubscriptionProperties: TeacherBundleSubscriptionProperties,
) : TeacherBundlePriceService {
    override fun getMonthlyBundles() =
        TeacherBundlePrice(
            teacherBundleSubscriptionProperties.monthly.price,
            teacherBundleSubscriptionProperties.monthly.discountPercent,
        )

    override fun getHalfYearlyBundles() =
        TeacherBundlePrice(
            teacherBundleSubscriptionProperties.halfYearly.price,
            teacherBundleSubscriptionProperties.halfYearly.discountPercent,
        )

    override fun getYearlyBundles() =
        TeacherBundlePrice(
            teacherBundleSubscriptionProperties.yearly.price,
            teacherBundleSubscriptionProperties.yearly.discountPercent,
        )

    override fun getBundlePrice(frequency: SubscriptionFrequency): TeacherBundlePrice {
        val properties =
            when (frequency) {
                SubscriptionFrequency.MONTHLY -> teacherBundleSubscriptionProperties.monthly
                SubscriptionFrequency.YEARLY -> teacherBundleSubscriptionProperties.yearly
                SubscriptionFrequency.HALF_YEARLY -> teacherBundleSubscriptionProperties.halfYearly
            }
        return TeacherBundlePrice(properties.price, properties.discountPercent)
    }
}

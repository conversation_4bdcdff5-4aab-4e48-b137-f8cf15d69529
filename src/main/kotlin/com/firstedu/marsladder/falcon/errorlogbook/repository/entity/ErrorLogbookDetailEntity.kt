package com.firstedu.marsladder.falcon.errorlogbook.repository.entity

import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookEvent
import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookStatus
import com.firstedu.marsladder.falcon.practice.repository.entity.Question
import com.firstedu.marsladder.falcon.question.QuestionDifficulty
import com.firstedu.marsladder.falcon.question.QuestionLikeOrDislike
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Id
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.GenericGenerator
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.time.LocalDateTime

@Entity(name = "view_error_logbook_detail")
data class ErrorLogbookDetailEntity(
    @Id
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    var id: String? = null,
    val cognitoUid: String,
    val courseId: String,
    val areaId: String,
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "topic_ids", columnDefinition = "json")
    val topicIds: List<String>,
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "subtopic_ids", columnDefinition = "json")
    val subtopicIds: List<String>,
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "question", columnDefinition = "json")
    val question: Question,
    val optionId: String,
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "error_causes", columnDefinition = "json")
    var errorCauses: List<String>,
    @Enumerated(EnumType.STRING)
    var status: ErrorLogbookStatus,
    var favorite: Boolean,
    var deleted: Boolean,
    @Enumerated(EnumType.ORDINAL)
    val difficulty: QuestionDifficulty,
    val questionId: String,
    val practiceId: String,
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "content_tags", columnDefinition = "json")
    val contentTags: List<String>,
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "area_ids", columnDefinition = "json")
    val areaIds: List<String>,
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "events", columnDefinition = "json")
    var events: List<ErrorLogbookEvent>,
    @CreationTimestamp
    @Column(updatable = false)
    val createdAt: LocalDateTime? = null,
    @Enumerated(EnumType.STRING)
    val likeOrDislike: QuestionLikeOrDislike?,
)

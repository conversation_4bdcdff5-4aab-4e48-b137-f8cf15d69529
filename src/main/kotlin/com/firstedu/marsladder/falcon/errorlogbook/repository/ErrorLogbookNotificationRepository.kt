package com.firstedu.marsladder.falcon.errorlogbook.repository

import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookNotificationType
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorLogbookNotificationEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface ErrorLogbookNotificationRepository :
    JpaRepository<ErrorLogbookNotificationEntity, String> {
    fun findOneByCognitoUidAndCourseIdAndTypeAndRead(
        cognitoUid: String,
        courseId: String,
        type: ErrorLogbookNotificationType,
        read: Boolean = false,
    ): Optional<ErrorLogbookNotificationEntity>

    fun findOneByCognitoUidAndCourseIdAndType(
        cognitoUid: String,
        courseId: String,
        type: ErrorLogbookNotificationType,
    ): Optional<ErrorLogbookNotificationEntity>
}

package com.firstedu.marsladder.falcon.principal.service.domain

import com.firstedu.marsladder.falcon.principal.repository.entity.SchoolCampusEntity
import com.firstedu.marsladder.falcon.principal.repository.entity.SchoolTeacherCampusProfileEntity

data class SchoolTeacherCampusProfile(
    val schoolId: String,
    val campusId: String,
    val campusName: String? = null,
    val teacherId: String,
    val teacherSubjects: List<String>?,
    val teacherGrades: List<String>?,
    val seatingCapability: Int?,
    val removed: Boolean,
) {
    companion object {
        fun from(
            schoolTeacherCampusProfileEntity: SchoolTeacherCampusProfileEntity,
            schoolCampusEntity: SchoolCampusEntity,
        ) =
            SchoolTeacherCampusProfile(
                schoolId = schoolTeacherCampusProfileEntity.schoolId,
                campusId = schoolTeacherCampusProfileEntity.campusId,
                campusName = schoolCampusEntity.campusName,
                teacherId = schoolTeacherCampusProfileEntity.teacherId,
                teacherSubjects = schoolTeacherCampusProfileEntity.teacherSubjects,
                teacherGrades = schoolTeacherCampusProfileEntity.teacherGrades,
                seatingCapability = schoolTeacherCampusProfileEntity.seatingCapability,
                removed = schoolTeacherCampusProfileEntity.removed,
            )
    }

    fun toEntity() =
        SchoolTeacherCampusProfileEntity(
            schoolId = this.schoolId,
            campusId = this.campusId,
            teacherId = this.teacherId,
            teacherSubjects = this.teacherSubjects,
            teacherGrades = this.teacherGrades,
            seatingCapability = this.seatingCapability,
            removed = this.removed,
        )
}

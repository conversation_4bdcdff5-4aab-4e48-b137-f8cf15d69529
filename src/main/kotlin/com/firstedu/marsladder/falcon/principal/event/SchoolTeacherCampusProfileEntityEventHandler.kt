package com.firstedu.marsladder.falcon.principal.event

import com.firstedu.marsladder.falcon.principal.repository.entity.SchoolTeacherCampusProfileEntity
import jakarta.persistence.PostPersist
import jakarta.persistence.PostUpdate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component

@Component
class SchoolTeacherCampusProfileEntityEventHandler {
    @Lazy
    @Autowired
    private lateinit var publisher: ApplicationEventPublisher

    @PostPersist
    @PostUpdate
    fun handle(schoolTeacherCampusProfileEntity: SchoolTeacherCampusProfileEntity) {
        publisher.publishEvent(
            SchoolTeacherCampusProfileUpdateEvent(
                source = this,
                teacherId = schoolTeacherCampusProfileEntity.teacherId,
                schoolTeacherCampusProfileId = schoolTeacherCampusProfileEntity.id!!,
            ),
        )
    }
}

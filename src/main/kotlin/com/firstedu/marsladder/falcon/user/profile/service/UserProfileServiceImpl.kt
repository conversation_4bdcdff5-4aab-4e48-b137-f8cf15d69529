package com.firstedu.marsladder.falcon.user.profile.service

import com.firstedu.marsladder.falcon.classroom.service.ClassroomStudentService
import com.firstedu.marsladder.falcon.discount.service.DiscountService
import com.firstedu.marsladder.falcon.discount.service.exception.DiscountNotFoundException
import com.firstedu.marsladder.falcon.practice.service.PracticeService
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherProfileNotFoundException
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherProfileService
import com.firstedu.marsladder.falcon.subscription.repository.SubscriptionRepository
import com.firstedu.marsladder.falcon.subscription.service.impl.SubscriptionChecker
import com.firstedu.marsladder.falcon.task.service.TaskService
import com.firstedu.marsladder.falcon.tutor.tutorprofile.service.TutorProfileNotFoundException
import com.firstedu.marsladder.falcon.tutor.tutorprofile.service.TutorProfileService
import com.firstedu.marsladder.falcon.user.identity.service.UserService
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class UserProfileServiceImpl(
    private val userService: UserService,
    private val tutorProfileService: TutorProfileService,
    private val teacherProfileService: TeacherProfileService,
    private val subscriptionRepository: SubscriptionRepository,
    private val taskService: TaskService,
    private val classroomStudentService: ClassroomStudentService,
    private val discountService: DiscountService,
    private val practiceService: PracticeService,
    private val subscriptionChecker: SubscriptionChecker,
) : UserProfileService {
    override fun getUserProfile(userId: String): UserProfile {
        var hasReceivedVoucher = false
        var voucherValue: BigDecimal = BigDecimal.ZERO
        try {
            val discount = discountService.getLatestDiscount(userId, LocalDateTime.now().minusDays(3L))
            hasReceivedVoucher = true
            voucherValue = BigDecimal.valueOf(discount.amountOff.toLong()).divide(BigDecimal.TEN).divide(BigDecimal.TEN)
        } catch (_: DiscountNotFoundException) {
        }

        val allSubscriptions = findAllSubscriptions(userId)
        val userProfile =
            UserProfile(
                userId = userId,
                phoneNumber = null,
                isEverLoggedIn = userService.hasUserEverLoggedIn(userId),
                consumerName = null,
                isTeacher = false,
                teacherName = null,
                teacherPosition = null,
                isTutor = false,
                tutorFirstName = null,
                tutorLastName = null,
                isCurrentlySubscribed = allSubscriptions.any { subscriptionChecker.isSubscriptionActive(it) },
                isPreviouslySubscribed = allSubscriptions.isNotEmpty(),
                invitedStudentCount = classroomStudentService.getInvitedStudentCount(userId),
                createdTaskCount = taskService.getTaskCountFromDate(userId, LocalDateTime.now().minusDays(7L)),
                answeredQuestionCount =
                    practiceService.getAnsweredQuestionCountFromDate(
                        userId,
                        LocalDateTime.now().minusDays(7L),
                    ),
                hasReceivedVoucher = hasReceivedVoucher,
                voucherValue = voucherValue,
            )

        val userProfileWithTeacherFields = setTeacherFields(userId, userProfile)
        return setTutorFields(userId, userProfileWithTeacherFields)
    }

    private fun setTeacherFields(
        userId: String,
        userProfile: UserProfile,
    ): UserProfile {
        var isTeacher: Boolean
        var teacherPosition: String
        var teacherName: String
        var phoneNumber: String?
        try {
            val teacherProfile = teacherProfileService.getProfile(userId)
            isTeacher = true
            teacherPosition = teacherProfile.positionName
            teacherName = teacherProfile.nickname
            phoneNumber = teacherProfile.phoneNumber
        } catch (e: TeacherProfileNotFoundException) {
            isTeacher = false
            teacherPosition = ""
            teacherName = ""
            phoneNumber = null
        }

        return UserProfile(
            userId = userProfile.userId,
            phoneNumber = phoneNumber,
            isEverLoggedIn = userProfile.isEverLoggedIn,
            consumerName = userProfile.consumerName,
            isTeacher = isTeacher,
            teacherName = teacherName,
            isTutor = userProfile.isTutor,
            tutorFirstName = userProfile.tutorFirstName,
            tutorLastName = userProfile.tutorLastName,
            isCurrentlySubscribed = userProfile.isCurrentlySubscribed,
            isPreviouslySubscribed = userProfile.isPreviouslySubscribed,
            teacherPosition = teacherPosition,
            invitedStudentCount = userProfile.invitedStudentCount,
            createdTaskCount = userProfile.createdTaskCount,
            answeredQuestionCount = userProfile.answeredQuestionCount,
            hasReceivedVoucher = userProfile.hasReceivedVoucher,
            voucherValue = userProfile.voucherValue,
        )
    }

    private fun setTutorFields(
        userId: String,
        userProfile: UserProfile,
    ): UserProfile {
        var isTutor: Boolean
        var tutorFirstName: String?
        var tutorLastName: String?
        var phoneNumber: String?
        try {
            val tutorProfile = tutorProfileService.getProfile(userId)
            isTutor = true
            tutorFirstName = tutorProfile.firstName
            tutorLastName = tutorProfile.lastName
            phoneNumber = tutorProfile.phoneNumber
        } catch (e: TutorProfileNotFoundException) {
            isTutor = false
            tutorFirstName = null
            tutorLastName = null
            phoneNumber = null
        }

        return UserProfile(
            userId = userProfile.userId,
            phoneNumber = phoneNumber,
            isEverLoggedIn = userProfile.isEverLoggedIn,
            consumerName = userProfile.consumerName,
            isTeacher = userProfile.isTeacher,
            teacherName = userProfile.teacherName,
            isTutor = isTutor,
            tutorFirstName = tutorFirstName,
            tutorLastName = tutorLastName,
            isCurrentlySubscribed = userProfile.isCurrentlySubscribed,
            isPreviouslySubscribed = userProfile.isPreviouslySubscribed,
            teacherPosition = userProfile.teacherPosition,
            invitedStudentCount = userProfile.invitedStudentCount,
            createdTaskCount = userProfile.createdTaskCount,
            answeredQuestionCount = userProfile.answeredQuestionCount,
            hasReceivedVoucher = userProfile.hasReceivedVoucher,
            voucherValue = userProfile.voucherValue,
        )
    }

    private fun findAllSubscriptions(cognitoUid: String) = subscriptionRepository.findByCognitoUid(cognitoUid)
}

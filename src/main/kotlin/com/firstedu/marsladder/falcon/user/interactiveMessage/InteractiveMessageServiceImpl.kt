package com.firstedu.marsladder.falcon.user.interactiveMessage

import org.springframework.stereotype.Service

@Service
class InteractiveMessageServiceImpl(
    val interactiveMessageRepository: InteractiveMessageRepository,
) : InteractiveMessageService {
    override fun setViewResultInteractiveMessage(cognitoUid: String) {
        val entity = InteractiveMessageEntity(cognitoUid = cognitoUid, viewResult = true)
        interactiveMessageRepository.save(entity)
    }

    override fun hasViewedResultInteractiveMessage(cognitoUid: String): Boolean {
        val interactiveMessage = interactiveMessageRepository.findById(cognitoUid)
        if (interactiveMessage.isEmpty) return false
        return interactiveMessage.get().viewResult
    }
}

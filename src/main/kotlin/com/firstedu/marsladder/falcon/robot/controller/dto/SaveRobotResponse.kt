package com.firstedu.marsladder.falcon.robot.controller.dto

import com.firstedu.marsladder.falcon.utils.calculateAverageAccuracy
import com.firstedu.marsladder.falcon.utils.calculateAverageCompletionTime

data class SaveRobotResponse(
    val id: String,
    val courseId: String,
    val rankId: String,
    val rankName: String,
    val minAccuracy: Int,
    val maxAccuracy: Int,
    val minCompletionMinute: Int,
    val minCompletionSecond: Int,
    val maxCompletionMinute: Int,
    val maxCompletionSecond: Int,
    val averageAccuracy: Double = calculateAverageAccuracy(minAccuracy, maxAccuracy),
    val averageCompletionTime: String =
        calculateAverageCompletionTime(
            minCompletionMinute,
            minCompletionSecond,
            maxCompletionMinute,
            maxCompletionSecond,
        ),
)

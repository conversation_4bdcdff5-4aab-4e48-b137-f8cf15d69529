package com.firstedu.marsladder.falcon.game.service.impl

import com.firstedu.marsladder.falcon.game.repository.PlayerMissionRepository
import com.firstedu.marsladder.falcon.game.repository.entity.PlayerMissionEntity
import com.firstedu.marsladder.falcon.game.repository.entity.toDomain
import com.firstedu.marsladder.falcon.game.service.PlayerMissionService
import com.firstedu.marsladder.falcon.game.service.domain.PlayerMission
import com.firstedu.marsladder.falcon.game.service.exception.PlayerMissionExistedException
import com.firstedu.marsladder.falcon.game.service.exception.PlayerMissionNotFoundException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class PlayerMissionServiceImpl(private val playerMissionRepository: PlayerMissionRepository) : PlayerMissionService {
    private val logger = LoggerFactory.getLogger(PlayerMissionService::class.java)

    override fun saveMission(playerMission: PlayerMission): PlayerMission {
        logger.info("Save mission: ${playerMission.missionId} for user: ${playerMission.cognitoUid} in chapter: ${playerMission.chapterId}")
        playerMissionRepository.findByCognitoUidAndChapterIdAndMissionId(
            playerMission.cognitoUid,
            playerMission.chapterId,
            playerMission.missionId,
        ).ifPresent {
            logger.error(
                "User: ${playerMission.cognitoUid} has existed mission: ${playerMission.missionId} in chapter: ${playerMission.chapterId}",
            )
            throw PlayerMissionExistedException("Existed missionId: ${playerMission.missionId} in chapter ${playerMission.chapterId}")
        }

        return playerMissionRepository.save(
            PlayerMissionEntity(
                cognitoUid = playerMission.cognitoUid,
                chapterId = playerMission.chapterId,
                missionId = playerMission.missionId,
            ),
        ).toDomain()
    }

    override fun getPlayerMissionsOfAChapter(
        cognitoUid: String,
        chapterId: String,
    ): List<PlayerMission> {
        return playerMissionRepository.findByCognitoUidAndChapterId(cognitoUid, chapterId)
            .map(PlayerMissionEntity::toDomain)
    }

    override fun getPlayerMissionsOfCurrentChapter(cognitoUid: String): List<PlayerMission> {
        return playerMissionRepository.findFirstByCognitoUidOrderByCreatedAtDesc(cognitoUid)
            .map {
                getPlayerMissionsOfAChapter(cognitoUid, it.chapterId)
            }
            .orElseThrow { PlayerMissionNotFoundException("Could not found current player mission") }
    }
}

package com.firstedu.marsladder.falcon.game.service.impl

import com.firstedu.marsladder.falcon.game.repository.LadderRepository
import com.firstedu.marsladder.falcon.game.service.LadderService
import com.firstedu.marsladder.falcon.game.service.domain.Ladder
import com.firstedu.marsladder.falcon.game.service.exception.PlayerLadderNotFoundException
import org.springframework.stereotype.Service

@Service
class LadderImpl(private val ladderRepository: LadderRepository) : LadderService {
    override fun getLadder(id: String): Ladder {
        val ladderEntity = ladderRepository.findById(id).orElseThrow { PlayerLadderNotFoundException("Ladder $id not found.") }
        return Ladder(
            id = ladderEntity.id!!,
            type = ladderEntity.type,
            goldAmount = ladderEntity.goldAmount,
            buildingDuration = ladderEntity.buildingDuration,
        )
    }
}

package com.firstedu.marsladder.falcon.facebook.presentation

import com.firstedu.marsladder.falcon.facebook.service.UserDataDeletionService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.util.UriComponentsBuilder

// https://developers.facebook.com/docs/development/create-an-app/app-dashboard/data-deletion-callback
@RestController
@RequestMapping("facebook")
class UserDataDeletionController(
    val userDataDeletionService: UserDataDeletionService,
    @Value("\${facebook.deletion-status-url}")
    val facebookDeletionStatusUrl: String,
) {
    @PostMapping("user-data-deletion")
    fun deleteUserData(userDataDeletionRequest: UserDataDeletionRequest): UserDataDeletionResponse {
        val requestId = userDataDeletionService.deleteUserData(userDataDeletionRequest.signed_request)
        val url =
            UriComponentsBuilder.fromUriString(facebookDeletionStatusUrl)
                .path(requestId)
                .build()
                .toUri()
                .toURL()
        return UserDataDeletionResponse(url.toString(), requestId)
    }

    @PostMapping("user-data-deletion-debug")
    fun deleteUserDataDebug(
        @RequestHeader headers: HttpHeaders,
        @RequestBody userDataDeletionRequest: String,
    ) {
        logger.info(headers.toString())
        logger.info(userDataDeletionRequest)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}

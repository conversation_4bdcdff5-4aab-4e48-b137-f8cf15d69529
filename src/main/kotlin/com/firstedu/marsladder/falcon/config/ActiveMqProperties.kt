package com.firstedu.marsladder.falcon.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = "activemq")
data class ActiveMqProperties(
    var user: String = "user",
    var password: String = "password",
    var port: Int = 61614,
    var endpoint: String = "localhost",
)

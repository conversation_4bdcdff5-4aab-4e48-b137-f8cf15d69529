package com.firstedu.marsladder.falcon.following.service

import com.firstedu.marsladder.falcon.following.repository.FollowInformationRepositoryEntity
import com.firstedu.marsladder.falcon.following.repository.FollowRepository
import com.firstedu.marsladder.falcon.following.repository.FollowRepositoryEntity
import com.firstedu.marsladder.falcon.profile.repository.ProfileRepository
import com.firstedu.marsladder.falcon.profile.repository.entity.ProfileEntity
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class FollowServiceImpl(
    private val followRepository: FollowRepository,
    private val profileRepository: ProfileRepository,
) : FollowService {
    override fun getFollows(
        followerCognitoUid: String,
        pageable: Pageable,
    ): Page<FollowServiceEntity> {
        return followRepository
            .findByFollowerCognitoUid(followerCognitoUid, pageable)
            .map(this::buildServiceEntity)
    }

    override fun getFollowsByClassroom(
        followerCognitoUid: String,
        classroomId: String,
    ): List<FollowServiceEntity> {
        return followRepository
            .findByFollowerCognitoUidAndClassroomId(followerCognitoUid, classroomId)
            .map(this::buildServiceEntity)
    }

    override fun getFollowProfiles(
        followerCognitoUid: String,
        nicknameKeyword: String,
    ): List<FollowProfile> {
        return profileRepository.findTop5ByNicknameContainsIgnoreCaseOrderByLevelDesc(nicknameKeyword)
            .map {
                FollowProfile(
                    it.cognitoUid!!,
                    it.nickname!!,
                    followRepository.existsByFollowerCognitoUidAndFolloweeCognitoUid(followerCognitoUid, it.cognitoUid),
                )
            }
    }

    override fun updateFolloweeRemark(
        followerCognitoUid: String,
        followId: String,
        followeeRemark: String,
    ): FollowServiceEntity {
        val follow =
            followRepository.findById(followId)
                .orElseThrow { FollowNotFoundException() }
        if (follow.followerCognitoUid == followerCognitoUid) {
            val followInformation = follow.followInformation ?: throw FollowInformationIncompleteException()
            followInformation.followeeAlias = followeeRemark
            return buildServiceEntity(followRepository.save(follow))
        } else {
            throw FollowNotFoundForFollowerException()
        }
    }

    override fun follow(
        followerCognitoUid: String,
        followeeCognitoUid: String,
    ): FollowServiceEntity {
        val profile =
            profileRepository
                .findByCognitoUid(followeeCognitoUid)
                .orElseThrow {
                    FolloweeNotFoundException(followeeCognitoUid, "The followee doesn't exist!")
                }

        val followRepositoryEntity =
            FollowRepositoryEntity(
                followerCognitoUid = followerCognitoUid,
                followeeCognitoUid = followeeCognitoUid,
            )
        val followInformationRepositoryEntity =
            FollowInformationRepositoryEntity(
                followeeAlias = "",
                follow = followRepositoryEntity,
            )
        followRepositoryEntity.followInformation = followInformationRepositoryEntity

        try {
            val savedFollow = followRepository.saveAndFlush(followRepositoryEntity)
            return buildServiceEntity(savedFollow, profile)
        } catch (e: DataIntegrityViolationException) {
            throw DuplicatedFolloweeException(
                followerCognitoUid,
                followeeCognitoUid,
                e,
            )
        }
    }

    private fun buildServiceEntity(
        repositoryEntity: FollowRepositoryEntity,
        profile: ProfileEntity,
    ): FollowServiceEntity {
        return FollowServiceEntity(
            repositoryEntity.id!!,
            repositoryEntity.followerCognitoUid,
            repositoryEntity.followeeCognitoUid,
            repositoryEntity.createdAt!!,
            repositoryEntity.followInformation?.followeeAlias ?: "",
            FolloweeProfileServiceEntity(
                nickname = profile.nickname ?: repositoryEntity.followeeCognitoUid,
                level = profile.level ?: 0,
                coins = profile.gold ?: 0,
                avatarUrl = profile.avatar ?: "",
            ),
        )
    }

    private fun buildServiceEntity(repositoryEntity: FollowRepositoryEntity): FollowServiceEntity {
        return FollowServiceEntity(
            repositoryEntity.id!!,
            repositoryEntity.followerCognitoUid,
            repositoryEntity.followeeCognitoUid,
            repositoryEntity.createdAt!!,
            repositoryEntity.followInformation?.followeeAlias ?: "",
            if (repositoryEntity.followee == null) {
                FolloweeProfileServiceEntity(
                    nickname = repositoryEntity.followeeCognitoUid,
                    level = 0,
                    coins = 0,
                    avatarUrl = "",
                )
            } else {
                FolloweeProfileServiceEntity(
                    nickname = repositoryEntity.followee.nickname ?: repositoryEntity.followeeCognitoUid,
                    level = repositoryEntity.followee.level ?: 0,
                    coins = repositoryEntity.followee.gold ?: 0,
                    avatarUrl = repositoryEntity.followee.avatar ?: "",
                )
            },
        )
    }

    override fun unFollow(
        followerCognitoUid: String,
        followeeCognitoUid: String,
    ) {
        val affectedRows =
            followRepository.deleteByFollowerCognitoUidAndFolloweeCognitoUid(
                followerCognitoUid,
                followeeCognitoUid,
            )
        if (affectedRows < 1) {
            throw FollowNotFoundException()
        }
    }

    override fun deleteFollow(id: String) {
        if (followRepository.existsById(id)) {
            followRepository.deleteById(id)
        } else {
            throw FollowNotFoundException()
        }
    }
}

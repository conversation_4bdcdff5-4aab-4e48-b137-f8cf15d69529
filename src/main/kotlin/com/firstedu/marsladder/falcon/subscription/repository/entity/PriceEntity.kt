package com.firstedu.marsladder.falcon.subscription.repository.entity

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Id
import org.hibernate.annotations.GenericGenerator
import org.hibernate.envers.Audited

@Entity(name = "course_price")
@Audited
data class PriceEntity(
    @Id
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    var id: String? = null,
    var courseId: String? = null,
    var price: Double? = null,
    var frequency: String? = null,
    var promotionalPrice: Double? = null,
    var promotionMonths: Int? = null,
)

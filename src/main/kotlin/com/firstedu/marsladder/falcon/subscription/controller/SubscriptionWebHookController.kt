package com.firstedu.marsladder.falcon.subscription.controller

import com.firstedu.marsladder.falcon.subscription.service.exception.InvalidStripeEventException
import com.firstedu.marsladder.falcon.subscription.service.exception.InvalidStripeSignatureException
import com.firstedu.marsladder.falcon.subscription.service.strategy.WebhookStrategyContext
import com.stripe.exception.SignatureVerificationException
import com.stripe.model.Event
import com.stripe.net.Webhook
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("subscriptions")
class SubscriptionWebHookController(
    private val webhookStrategyContext: WebhookStrategyContext,
) {
    @Value("\${subscription.stripe.webhook-secret:}")
    private val secret: String = ""

    private val logger = LoggerFactory.getLogger(SubscriptionWebHookController::class.java)

    @PostMapping("/webhook")
    fun handleWebhook(
        @RequestBody payload: String,
        @RequestHeader("Stripe-Signature") sigHeader: String,
    ): ResponseEntity<String> {
        val event = constructEvent(payload, sigHeader)
        val dataObjectDeserializer = event.dataObjectDeserializer
        val stripeObject =
            dataObjectDeserializer.getObject().orElseGet {
                val unsafeObject = dataObjectDeserializer.deserializeUnsafe()
                logger.warn("[STRIPE WEBHOOK] Unsafe deserialization used for object: ${unsafeObject.toJson()}")
                unsafeObject
            }
        try {
            webhookStrategyContext.handleWebhook(
                eventType = event.type,
                stripeObject = stripeObject,
            )
        } catch (e: InvalidStripeEventException) {
            return ResponseEntity.noContent().build()
        }

        return ResponseEntity.ok("Stripe event ${event.type} handle successfully")
    }

    private fun constructEvent(
        payload: String,
        sigHeader: String,
    ): Event {
        val event: Event
        try {
            event = Webhook.constructEvent(payload, sigHeader, secret)
        } catch (e: SignatureVerificationException) {
            logger.error("[STRIPE WEBHOOK] Stripe event signature verification failed", e)
            throw InvalidStripeSignatureException("[STRIPE WEBHOOK] Stripe event signature verification failed")
        }
        return event
    }
}

package com.firstedu.marsladder.falcon.subscription.service.impl

import com.firstedu.marsladder.falcon.subscription.repository.entity.SubscriptionEntity
import com.firstedu.marsladder.falcon.utils.DateTimeUtil
import org.springframework.stereotype.Component

@Component
class SubscriptionChecker(
    val dateTimeUtil: DateTimeUtil,
) {
    fun isSubscriptionActive(subscriptionEntity: SubscriptionEntity): Boolean {
        val now = dateTimeUtil.getNowTime()
        if (subscriptionEntity.isTrial) {
            if (subscriptionEntity.isTrialConvertedEarly) {
                return false
            } else {
                return now.isBefore(subscriptionEntity.endAt)
            }
        } else {
            return subscriptionEntity.endAt == null || subscriptionEntity.endAt!!.isAfter(now)
        }
    }
}

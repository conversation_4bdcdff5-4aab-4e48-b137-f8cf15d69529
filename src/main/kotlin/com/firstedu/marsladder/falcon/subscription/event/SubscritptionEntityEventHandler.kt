package com.firstedu.marsladder.falcon.subscription.event

import com.firstedu.marsladder.falcon.subscription.repository.entity.SubscriptionEntity
import jakarta.persistence.PostPersist
import jakarta.persistence.PostUpdate
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component

@Component
class SubscriptionEntityEventHandler {
    @Lazy
    @Autowired
    private lateinit var publisher: ApplicationEventPublisher

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(SubscriptionEntityEventHandler::class.java)
    }

    @PostPersist
    @PostUpdate
    fun handleStudentBundleChanged(entity: SubscriptionEntity) {
        logger.info("[Event Publish] Subscription persisted or updated: ${entity.id}")
        publisher.publishEvent(StudentSubscriptionChanged(entity.id!!))
    }
}

data class StudentSubscriptionChanged(val subscriptionId: String)

package com.firstedu.marsladder.falcon.profile.controller

import com.firstedu.marsladder.falcon.profile.controller.dto.GetAllRanksResponse
import com.firstedu.marsladder.falcon.profile.controller.dto.GetRankingResponse
import com.firstedu.marsladder.falcon.profile.controller.dto.GetStudentRankResponse
import com.firstedu.marsladder.falcon.profile.service.StudentRankService
import com.firstedu.marsladder.falcon.profile.service.domain.StudentRank
import com.firstedu.marsladder.falcon.rank.service.RankService
import com.firstedu.marsladder.falcon.security.SessionProvider
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("student_ranks")
class StudentRankController(
    private val studentRankService: StudentRankService,
    private val rankService: RankService,
    private val sessionProvider: SessionProvider,
) {
    @GetMapping("me")
    fun getStudentRanks(): ResponseEntity<List<GetStudentRankResponse>> {
        val studentRanks = studentRankService.getStudentRanks(sessionProvider.getUserId())
        return ResponseEntity.status(HttpStatus.OK).body(
            studentRanks.map { studentRank ->
                GetStudentRankResponse(
                    id = studentRank.id!!,
                    courseId = studentRank.courseId,
                    cognitoUid = studentRank.cognitoUid,
                    rankLevel = studentRank.rankLevel,
                    rankName = rankService.findRankByRankLevel(studentRank.rankLevel).name,
                    star = studentRank.star,
                    totalAnswer = studentRank.totalAnswer,
                    totalCorrectAnswer = studentRank.totalCorrectAnswer,
                    totalTime = studentRank.totalTime.toMinutes(),
                    areaOfStudyId = null,
                )
            },
        )
    }

    @GetMapping("{courseId}/me")
    fun getStudentRankByCourse(
        @PathVariable courseId: String,
    ): ResponseEntity<GetStudentRankResponse> {
        val cognitoUid = sessionProvider.getUserId()
        val studentRank =
            studentRankService.getStudentRankByCognitoUidAndCourseId(
                cognitoUid = cognitoUid,
                courseId = courseId,
            )
        if (studentRank == null) {
            val defaultRankLevel = 0
            val defaultStar = 0
            return ResponseEntity.status(HttpStatus.OK).body(
                GetStudentRankResponse(
                    courseId = courseId,
                    cognitoUid = cognitoUid,
                    rankLevel = defaultRankLevel,
                    rankName = rankService.findRankByRankLevel(defaultRankLevel).name,
                    star = defaultStar,
                    totalAnswer = 0,
                    totalCorrectAnswer = 0,
                    totalTime = 0,
                    areaOfStudyId = null,
                ),
            )
        } else {
            return ResponseEntity.status(HttpStatus.OK).body(
                GetStudentRankResponse(
                    id = studentRank.id,
                    courseId = studentRank.courseId,
                    cognitoUid = studentRank.cognitoUid,
                    rankLevel = studentRank.rankLevel,
                    rankName = rankService.findRankByRankLevel(studentRank.rankLevel).name,
                    star = studentRank.star,
                    totalAnswer = studentRank.totalAnswer,
                    totalCorrectAnswer = studentRank.totalCorrectAnswer,
                    totalTime = studentRank.totalTime.toMinutes(),
                    areaOfStudyId = null,
                ),
            )
        }
    }

    @GetMapping("{courseId}/rankings")
    fun getStudentRankingInfoByCourseId(
        @PathVariable courseId: String,
    ) = ResponseEntity.status(HttpStatus.OK).body(
        with(studentRankService.getRankingBy(sessionProvider.getUserId(), courseId)) {
            GetRankingResponse(currentRank, totalStudents)
        },
    )

    @GetMapping("{courseId}/students")
    fun getAllStudentsRankByCourse(
        @PathVariable courseId: String,
        @PageableDefault(size = 100, page = 0) pageable: Pageable,
    ) = ResponseEntity.status(HttpStatus.OK).body(
        studentRankService.getAllStudentsRankingList(sessionProvider.getUserId(), courseId, pageable)
            .map(this::buildGetRankingListResponse),
    )

    @GetMapping("{courseId}/followees")
    fun getAllFolloweesRankByCourse(
        @PathVariable courseId: String,
        @PageableDefault(size = 100, page = 0) pageable: Pageable,
    ) = ResponseEntity.status(HttpStatus.OK).body(
        studentRankService.getAllFolloweesRankingList(sessionProvider.getUserId(), courseId, pageable)
            .map(this::buildGetRankingListResponse),
    )

    private fun buildGetRankingListResponse(it: StudentRank) =
        GetAllRanksResponse(
            avatarUrl = it.studentProfile.avatarUrl,
            nickname = it.studentProfile.nickname,
            rankName = rankService.findRankByRankLevel(it.rankLevel).name,
            rankLevel = it.rankLevel,
            level = it.studentProfile.level,
            coins = it.studentProfile.coins,
            star = it.star,
            alias = it.alias,
        )
}

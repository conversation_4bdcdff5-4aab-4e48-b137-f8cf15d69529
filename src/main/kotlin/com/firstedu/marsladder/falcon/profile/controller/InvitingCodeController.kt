package com.firstedu.marsladder.falcon.profile.controller

import com.firstedu.marsladder.falcon.profile.controller.dto.InvitingCodeResponse
import com.firstedu.marsladder.falcon.profile.service.InvitingCodeService
import com.firstedu.marsladder.falcon.security.SessionProvider
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@PreAuthorize("hasRole('CONSUMER')")
@RestController
@RequestMapping("inviting_codes")
class InvitingCodeController(
    private val invitingCodeService: InvitingCodeService,
    private val sessionProvider: SessionProvider,
) {
    @PostMapping
    fun save(): ResponseEntity<InvitingCodeResponse> {
        val invitingCode = invitingCodeService.save(sessionProvider.getUserId())
        return ResponseEntity.status(HttpStatus.CREATED).body(InvitingCodeResponse.from(invitingCode))
    }

    @GetMapping("me")
    @PreAuthorize("hasAnyRole('CONSUMER','SHARED')")
    fun get(): ResponseEntity<InvitingCodeResponse> {
        val invitingCode = invitingCodeService.getInvitingCodeByCognitoUid(sessionProvider.getUserId())
        return ResponseEntity.status(HttpStatus.OK).body(InvitingCodeResponse.from(invitingCode))
    }
}

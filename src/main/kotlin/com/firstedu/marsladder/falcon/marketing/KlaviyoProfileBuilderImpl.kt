package com.firstedu.marsladder.falcon.marketing

import com.firstedu.marsladder.falcon.klaviyo.profiles.KlaviyoProfile
import com.firstedu.marsladder.falcon.user.profile.service.UserProfile
import org.springframework.stereotype.Component

@Component
class KlaviyoProfileBuilderImpl : KlaviyoProfileBuilder {
    override fun build(
        email: String,
        userProfile: UserProfile,
    ): KlaviyoProfile {
        return KlaviyoProfile(
            email = email,
            phoneNumber = buildKlaviyoPhoneNumber(userProfile.phoneNumber),
            country = getCountry(userProfile.phoneNumber),
            firstName = buildKlaviyoProfileFirstName(userProfile),
            role = buildKlaviyoUserRole(userProfile),
            isEverLoggedIn = userProfile.isEverLoggedIn,
            isCurrentlySubscribed = userProfile.isCurrentlySubscribed,
            isPreviouslySubscribed = userProfile.isPreviouslySubscribed,
            teacherPosition = userProfile.teacherPosition,
            invitedStudentCount = userProfile.invitedStudentCount,
            createdTaskCount = userProfile.createdTaskCount,
            answeredQuestionCount = userProfile.answeredQuestionCount,
            hasReceivedVoucher = userProfile.hasReceivedVoucher,
            voucherValue = userProfile.voucherValue,
        )
    }

    private fun buildKlaviyoPhoneNumber(phoneNumber: String?): String? {
        return if (phoneNumber.isNullOrEmpty()) {
            phoneNumber
        } else {
            "+$phoneNumber"
        }
    }

    private fun buildKlaviyoUserRole(userProfile: UserProfile): String {
        return if (userProfile.isTeacher) {
            KlaviyoProfileBuilder.KLAVIYO_PROFILE_PROPERTY_USER_ROLE_TEACHER
        } else if (userProfile.isTutor) {
            KlaviyoProfileBuilder.KLAVIYO_PROFILE_PROPERTY_USER_ROLE_TUTOR
        } else {
            KlaviyoProfileBuilder.KLAVIYO_PROFILE_PROPERTY_USER_ROLE_STUDENT
        }
    }

    private fun buildKlaviyoProfileFirstName(userProfile: UserProfile): String? {
        return if (userProfile.isTeacher) {
            userProfile.teacherName
        } else if (userProfile.isTutor) {
            "${userProfile.tutorFirstName} ${userProfile.tutorLastName}"
        } else {
            userProfile.consumerName
        }
    }

    private fun getCountry(phoneNumber: String?): String? {
        if (phoneNumber == null) {
            return null
        }
        val countryPair = countries.find { phoneNumber.startsWith(it.first) }
        return countryPair?.second
    }

    val countries =
        listOf(
//            Pair("1", "Canada"),
            Pair("1", "United States"),
            Pair("7", "Kazakhstan"),
            Pair("7", "Russia"),
            Pair("20", "Egypt"),
            Pair("27", "South Africa"),
            Pair("30", "Greece"),
            Pair("31", "Netherlands"),
            Pair("32", "Belgium"),
            Pair("33", "France"),
            Pair("34", "Spain"),
            Pair("36", "Hungary"),
            Pair("39", "Italy"),
            Pair("40", "Romania"),
            Pair("41", "Switzerland"),
            Pair("43", "Austria"),
            Pair("44", "United Kingdom"),
            Pair("45", "Denmark"),
            Pair("46", "Sweden"),
            Pair("47", "Norway"),
            Pair("47", "Svalbard and Jan Mayen"),
            Pair("48", "Poland"),
            Pair("49", "Germany"),
            Pair("51", "Peru"),
            Pair("52", "Mexico"),
            Pair("53", "Cuba"),
            Pair("54", "Argentina"),
            Pair("55", "Brazil"),
            Pair("56", "Chile"),
            Pair("57", "Colombia"),
            Pair("58", "Venezuela"),
            Pair("60", "Malaysia"),
            Pair("61", "Australia"),
//            Pair("61", "Christmas Island"),
//            Pair("61", "Cocos Islands"),
            Pair("62", "Indonesia"),
            Pair("63", "Philippines"),
            Pair("64", "New Zealand"),
            Pair("64", "Pitcairn"),
            Pair("65", "Singapore"),
            Pair("66", "Thailand"),
            Pair("81", "Japan"),
            Pair("82", "South Korea"),
            Pair("84", "Vietnam"),
            Pair("86", "China"),
            Pair("90", "Turkey"),
            Pair("91", "India"),
            Pair("92", "Pakistan"),
            Pair("93", "Afghanistan"),
            Pair("94", "Sri Lanka"),
            Pair("95", "Myanmar"),
            Pair("98", "Iran"),
            Pair("211", "South Sudan"),
            Pair("212", "Morocco"),
            Pair("212", "Western Sahara"),
            Pair("213", "Algeria"),
            Pair("216", "Tunisia"),
            Pair("218", "Libya"),
            Pair("220", "Gambia"),
            Pair("221", "Senegal"),
            Pair("222", "Mauritania"),
            Pair("223", "Mali"),
            Pair("224", "Guinea"),
            Pair("225", "Ivory Coast"),
            Pair("226", "Burkina Faso"),
            Pair("227", "Niger"),
            Pair("228", "Togo"),
            Pair("229", "Benin"),
            Pair("230", "Mauritius"),
            Pair("231", "Liberia"),
            Pair("232", "Sierra Leone"),
            Pair("233", "Ghana"),
            Pair("234", "Nigeria"),
            Pair("235", "Chad"),
            Pair("236", "Central African Republic"),
            Pair("237", "Cameroon"),
            Pair("238", "Cape Verde"),
            Pair("239", "Sao Tome and Principe"),
            Pair("240", "Equatorial Guinea"),
            Pair("241", "Gabon"),
            Pair("242", "Republic of the Congo"),
            Pair("243", "Democratic Republic of the Congo"),
            Pair("244", "Angola"),
            Pair("245", "Guinea-Bissau"),
            Pair("246", "British Indian Ocean Territory"),
            Pair("248", "Seychelles"),
            Pair("249", "Sudan"),
            Pair("250", "Rwanda"),
            Pair("251", "Ethiopia"),
            Pair("252", "Somalia"),
            Pair("253", "Djibouti"),
            Pair("254", "Kenya"),
            Pair("255", "Tanzania"),
            Pair("256", "Uganda"),
            Pair("257", "Burundi"),
            Pair("258", "Mozambique"),
            Pair("260", "Zambia"),
            Pair("261", "Madagascar"),
            Pair("262", "Mayotte"),
            Pair("262", "Reunion"),
            Pair("263", "Zimbabwe"),
            Pair("264", "Namibia"),
            Pair("265", "Malawi"),
            Pair("266", "Lesotho"),
            Pair("267", "Botswana"),
            Pair("268", "Swaziland"),
            Pair("269", "Comoros"),
            Pair("290", "Saint Helena"),
            Pair("291", "Eritrea"),
            Pair("297", "Aruba"),
            Pair("298", "Faroe Islands"),
            Pair("299", "Greenland"),
            Pair("350", "Gibraltar"),
            Pair("351", "Portugal"),
            Pair("352", "Luxembourg"),
            Pair("353", "Ireland"),
            Pair("354", "Iceland"),
            Pair("355", "Albania"),
            Pair("356", "Malta"),
            Pair("357", "Cyprus"),
            Pair("358", "Finland"),
            Pair("359", "Bulgaria"),
            Pair("370", "Lithuania"),
            Pair("371", "Latvia"),
            Pair("372", "Estonia"),
            Pair("373", "Moldova"),
            Pair("374", "Armenia"),
            Pair("375", "Belarus"),
            Pair("376", "Andorra"),
            Pair("377", "Monaco"),
            Pair("378", "San Marino"),
            Pair("379", "Vatican"),
            Pair("380", "Ukraine"),
            Pair("381", "Serbia"),
            Pair("382", "Montenegro"),
            Pair("383", "Kosovo"),
            Pair("385", "Croatia"),
            Pair("386", "Slovenia"),
            Pair("387", "Bosnia and Herzegovina"),
            Pair("389", "Macedonia"),
            Pair("420", "Czech Republic"),
            Pair("421", "Slovakia"),
            Pair("423", "Liechtenstein"),
            Pair("500", "Falkland Islands"),
            Pair("501", "Belize"),
            Pair("502", "Guatemala"),
            Pair("503", "El Salvador"),
            Pair("504", "Honduras"),
            Pair("505", "Nicaragua"),
            Pair("506", "Costa Rica"),
            Pair("507", "Panama"),
            Pair("508", "Saint Pierre and Miquelon"),
            Pair("509", "Haiti"),
            Pair("590", "Saint Barthelemy"),
            Pair("590", "Saint Martin"),
            Pair("591", "Bolivia"),
            Pair("592", "Guyana"),
            Pair("593", "Ecuador"),
            Pair("595", "Paraguay"),
            Pair("597", "Suriname"),
            Pair("598", "Uruguay"),
            Pair("599", "Curacao"),
            Pair("599", "Netherlands Antilles"),
            Pair("670", "East Timor"),
            Pair("672", "Antarctica"),
            Pair("673", "Brunei"),
            Pair("674", "Nauru"),
            Pair("675", "Papua New Guinea"),
            Pair("676", "Tonga"),
            Pair("677", "Solomon Islands"),
            Pair("678", "Vanuatu"),
            Pair("679", "Fiji"),
            Pair("680", "Palau"),
            Pair("681", "Wallis and Futuna"),
            Pair("682", "Cook Islands"),
            Pair("683", "Niue"),
            Pair("685", "Samoa"),
            Pair("686", "Kiribati"),
            Pair("687", "New Caledonia"),
            Pair("688", "Tuvalu"),
            Pair("689", "French Polynesia"),
            Pair("690", "Tokelau"),
            Pair("691", "Micronesia"),
            Pair("692", "Marshall Islands"),
            Pair("850", "North Korea"),
            Pair("852", "Hong Kong"),
            Pair("853", "Macau"),
            Pair("855", "Cambodia"),
            Pair("856", "Laos"),
            Pair("880", "Bangladesh"),
            Pair("886", "Taiwan"),
            Pair("960", "Maldives"),
            Pair("961", "Lebanon"),
            Pair("962", "Jordan"),
            Pair("963", "Syria"),
            Pair("964", "Iraq"),
            Pair("965", "Kuwait"),
            Pair("966", "Saudi Arabia"),
            Pair("967", "Yemen"),
            Pair("968", "Oman"),
            Pair("970", "Palestine"),
            Pair("971", "United Arab Emirates"),
            Pair("972", "Israel"),
            Pair("973", "Bahrain"),
            Pair("974", "Qatar"),
            Pair("975", "Bhutan"),
            Pair("976", "Mongolia"),
            Pair("977", "Nepal"),
            Pair("992", "Tajikistan"),
            Pair("993", "Turkmenistan"),
            Pair("994", "Azerbaijan"),
            Pair("995", "Georgia"),
            Pair("996", "Kyrgyzstan"),
            Pair("998", "Uzbekistan"),
            Pair("1-242", "Bahamas"),
            Pair("1-246", "Barbados"),
            Pair("1-264", "Anguilla"),
            Pair("1-268", "Antigua and Barbuda"),
            Pair("1-284", "British Virgin Islands"),
            Pair("1-340", "U.S. Virgin Islands"),
            Pair("1-345", "Cayman Islands"),
            Pair("1-441", "Bermuda"),
            Pair("1-473", "Grenada"),
            Pair("1-649", "Turks and Caicos Islands"),
            Pair("1-664", "Montserrat"),
            Pair("1-670", "Northern Mariana Islands"),
            Pair("1-671", "Guam"),
            Pair("1-684", "American Samoa"),
            Pair("1-721", "Sint Maarten"),
            Pair("1-758", "Saint Lucia"),
            Pair("1-767", "Dominica"),
            Pair("1-784", "Saint Vincent and the Grenadines"),
            Pair("1-787, 1-939", "Puerto Rico"),
            Pair("1-809, 1-829, 1-849", "Dominican Republic"),
            Pair("1-868", "Trinidad and Tobago"),
            Pair("1-869", "Saint Kitts and Nevis"),
            Pair("1-876", "Jamaica"),
            Pair("44-1481", "Guernsey"),
            Pair("44-1534", "Jersey"),
            Pair("44-1624", "Isle of Man"),
        )
}

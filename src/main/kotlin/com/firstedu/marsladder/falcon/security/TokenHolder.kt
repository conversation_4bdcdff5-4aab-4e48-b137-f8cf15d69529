package com.firstedu.marsladder.falcon.security

import com.firstedu.marsladder.falcon.security.domain.Token
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal
import org.springframework.stereotype.Component

@Component
class TokenHolder {
    private val logger = LoggerFactory.getLogger(TokenHolder::class.java)

    fun getToken(): Token {
        return try {
            val principal =
                SecurityContextHolder.getContext().authentication?.principal?.let {
                    it as? OAuth2AuthenticatedPrincipal
                }
            val idToken = principal?.let { it.attributes["idToken"] }?.toString()
            val accessToken = principal?.let { it.attributes["accessToken"] }?.toString()
            Token(idToken, accessToken)
        } catch (e: Exception) {
            logger.warn("Cannot get token: {}", e.message)
            logger.debug("Cannot get token: {}", e.message, e)
            Token.empty()
        }
    }
}

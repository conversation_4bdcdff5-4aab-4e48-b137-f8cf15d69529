package com.firstedu.marsladder.falcon.robot.service.impl

import com.firstedu.marsladder.falcon.course.repository.CourseRepository
import com.firstedu.marsladder.falcon.course.repository.entity.CourseEntity
import com.firstedu.marsladder.falcon.course.service.exception.CourseNotFoundException
import com.firstedu.marsladder.falcon.rank.repository.RankRepository
import com.firstedu.marsladder.falcon.rank.repository.entity.RankEntity
import com.firstedu.marsladder.falcon.rank.service.exception.RankNotFoundException
import com.firstedu.marsladder.falcon.robot.repository.RobotRepository
import com.firstedu.marsladder.falcon.robot.repository.entity.RobotEntity
import com.firstedu.marsladder.falcon.robot.service.domain.Robot
import com.firstedu.marsladder.falcon.robot.service.exception.DuplicatedRobotException
import com.firstedu.marsladder.falcon.robot.service.exception.RobotNotFoundException
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import java.util.Optional

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
internal class RobotServiceImplTest {
    @InjectMocks
    private lateinit var robotService: RobotServiceImpl

    @Mock
    private lateinit var robotRepository: RobotRepository

    @Mock
    private lateinit var courseRepository: CourseRepository

    @Mock
    private lateinit var rankRepository: RankRepository

    private val mockId = "ab88dd31-ba28-493e-90d5-b5a4d127d6fd"
    private val mockCourseId = "16fd66e8-f97e-4111-b8dc-f82519256cd4"
    private val mockRankId = "83dce703-09f4-4090-8514-8080a5a1e6da"
    private val mockRankName = "Mock Rank"

    @Test
    internal fun `should return found robots given existed course and rank when get robots by courseId`() {
        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)
        val mockRobotRankEntities = listOf(mockRankEntity)
        val mockRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )
        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.of(mockCourseEntity))
        whenever(rankRepository.findByOrderByRankLevelAsc()).thenReturn(mockRobotRankEntities)
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.of(mockRobotEntity),
        )
        val robots = robotService.findRobotsByCourseId(mockCourseId)
        assertEquals(robots.size, mockRobotRankEntities.size)
        assertEquals(robots[0].id, mockRobotEntity.id)
        assertEquals(robots[0].courseId, mockRobotEntity.course?.id)
        assertEquals(robots[0].rankId, mockRobotEntity.rank?.id)
        assertEquals(robots[0].rankName, mockRobotEntity.rank?.name)
        assertEquals(robots[0].minAccuracy, mockRobotEntity.minAccuracy)
        assertEquals(robots[0].maxAccuracy, mockRobotEntity.maxAccuracy)
        assertEquals(robots[0].minCompletionMinute, mockRobotEntity.minCompletionMinute)
        assertEquals(robots[0].minCompletionSecond, mockRobotEntity.minCompletionSecond)
        assertEquals(robots[0].maxCompletionMinute, mockRobotEntity.maxCompletionMinute)
        assertEquals(robots[0].maxCompletionMinute, mockRobotEntity.maxCompletionMinute)
        assertEquals(robots[0].maxCompletionSecond, mockRobotEntity.maxCompletionSecond)
    }

    @Test
    internal fun `should throw CourseNotFoundException given not existed course when get robots by courseId`() {
        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)
        val mockRobotRankEntities = listOf(mockRankEntity)
        val mockRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )
        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.empty())
        whenever(rankRepository.findByOrderByRankLevelAsc()).thenReturn(mockRobotRankEntities)
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.of(mockRobotEntity),
        )
        assertThrows(CourseNotFoundException::class.java) {
            robotService.findRobotsByCourseId(mockCourseId)
        }
    }

    @Test
    internal fun `should return default robots given existed course and not existed rank when get robots by courseId`() {
        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)
        val mockRobotRankEntities = listOf(mockRankEntity)
        val mockRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )
        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.of(mockCourseEntity))
        whenever(rankRepository.findByOrderByRankLevelAsc()).thenReturn(mockRobotRankEntities)
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.empty(),
        )
        val robots = robotService.findRobotsByCourseId(mockCourseId)
        assertEquals(robots.size, mockRobotRankEntities.size)
        assertEquals(robots[0].id, null)
        assertEquals(robots[0].courseId, mockRobotEntity.course?.id)
        assertEquals(robots[0].rankId, mockRobotEntity.rank?.id)
        assertEquals(robots[0].rankName, mockRobotEntity.rank?.name)
        assertEquals(robots[0].minAccuracy, 0)
        assertEquals(robots[0].maxAccuracy, 0)
        assertEquals(robots[0].minCompletionMinute, 0)
        assertEquals(robots[0].minCompletionSecond, 0)
        assertEquals(robots[0].maxCompletionMinute, 0)
        assertEquals(robots[0].maxCompletionMinute, 0)
        assertEquals(robots[0].maxCompletionSecond, 0)
    }

    @Test
    internal fun `should return created robot given valid robot without id when save robot`() {
        val mockRobot =
            Robot(
                courseId = mockCourseId,
                rankId = mockRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)
        val mockRobotEntity =
            RobotEntity(
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )
        val mockSavedRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )
        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.of(mockCourseEntity))
        whenever(rankRepository.findById(mockRankId)).thenReturn(Optional.of(mockRankEntity))
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.empty(),
        )
        whenever(robotRepository.save(mockRobotEntity)).thenReturn(mockSavedRobotEntity)

        val savedRobot = robotService.save(mockRobot)

        assertEquals(savedRobot.id, mockSavedRobotEntity.id)
        assertEquals(savedRobot.courseId, mockRobotEntity.course?.id)
        assertEquals(savedRobot.rankId, mockRobotEntity.rank?.id)
        assertEquals(savedRobot.rankName, mockRobotEntity.rank?.name)
        assertEquals(savedRobot.minAccuracy, mockRobotEntity.minAccuracy)
        assertEquals(savedRobot.maxAccuracy, mockRobotEntity.maxAccuracy)
        assertEquals(savedRobot.minCompletionMinute, mockRobotEntity.minCompletionMinute)
        assertEquals(savedRobot.minCompletionSecond, mockRobotEntity.minCompletionSecond)
        assertEquals(savedRobot.maxCompletionMinute, mockRobotEntity.maxCompletionMinute)
        assertEquals(savedRobot.maxCompletionMinute, mockRobotEntity.maxCompletionMinute)
        assertEquals(savedRobot.maxCompletionSecond, mockRobotEntity.maxCompletionSecond)
    }

    @Test
    internal fun `should throw DuplicatedRobotException given existed courseId and rankId without id when save robot`() {
        val mockRobot =
            Robot(
                courseId = mockCourseId,
                rankId = mockRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)
        val mockSavedRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.of(mockSavedRobotEntity),
        )
        assertThrows(DuplicatedRobotException::class.java) { robotService.save(mockRobot) }
    }

    @Test
    internal fun `should throw CourseNotFoundException given not existed courseId without id when save robot`() {
        val mockRobot =
            Robot(
                courseId = mockCourseId,
                rankId = mockRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.empty())
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.empty(),
        )
        assertThrows(CourseNotFoundException::class.java) { robotService.save(mockRobot) }
    }

    @Test
    internal fun `should throw RobotRankNotFoundException given not existed rankId without id when save robot`() {
        val mockRobot =
            Robot(
                courseId = mockCourseId,
                rankId = mockRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.of(mockCourseEntity))
        whenever(rankRepository.findById(mockRankId)).thenReturn(Optional.empty())
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.empty(),
        )
        assertThrows(RankNotFoundException::class.java) { robotService.save(mockRobot) }
    }

    @Test
    internal fun `should return updated robot given valid robot with existed id when save robot`() {
        val mockRobot =
            Robot(
                id = mockId,
                courseId = mockCourseId,
                rankId = mockRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)

        val mockSavedRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )
        whenever(robotRepository.findById(mockId)).thenReturn(Optional.of(mockSavedRobotEntity))
        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.of(mockCourseEntity))
        whenever(rankRepository.findById(mockRankId)).thenReturn(Optional.of(mockRankEntity))
        whenever(robotRepository.findByCourse_IdAndRank_Id(mockCourseId, mockRankId)).thenReturn(
            Optional.empty(),
        )
        whenever(robotRepository.save(mockSavedRobotEntity)).thenReturn(mockSavedRobotEntity)

        val savedRobot = robotService.save(mockRobot)

        assertEquals(savedRobot.id, mockSavedRobotEntity.id)
        assertEquals(savedRobot.courseId, mockSavedRobotEntity.course?.id)
        assertEquals(savedRobot.rankId, mockSavedRobotEntity.rank?.id)
        assertEquals(savedRobot.rankName, mockSavedRobotEntity.rank?.name)
        assertEquals(savedRobot.minAccuracy, mockSavedRobotEntity.minAccuracy)
        assertEquals(savedRobot.maxAccuracy, mockSavedRobotEntity.maxAccuracy)
        assertEquals(savedRobot.minCompletionMinute, mockSavedRobotEntity.minCompletionMinute)
        assertEquals(savedRobot.minCompletionSecond, mockSavedRobotEntity.minCompletionSecond)
        assertEquals(savedRobot.maxCompletionMinute, mockSavedRobotEntity.maxCompletionMinute)
        assertEquals(savedRobot.maxCompletionMinute, mockSavedRobotEntity.maxCompletionMinute)
        assertEquals(savedRobot.maxCompletionSecond, mockSavedRobotEntity.maxCompletionSecond)
    }

    @Test
    internal fun `should throw RobotNotFoundException given valid robot with not existed id when save robot`() {
        val mockRobot =
            Robot(
                id = mockId,
                courseId = mockCourseId,
                rankId = mockRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        whenever(robotRepository.findById(mockId)).thenReturn(Optional.empty())
        assertThrows(RobotNotFoundException::class.java) { robotService.save(mockRobot) }
    }

    @Test
    internal fun `should throw DuplicatedRobotException given new existed courseId and rankId with existed id when save robot`() {
        val existedCourseId = "ab88dd31-ba28-493e-90d5-b5a4d127d600"
        val existedRankId = "ab88dd31-ba28-493e-90d5-b5a4d127d601"
        val mockRobot =
            Robot(
                id = mockId,
                courseId = existedCourseId,
                rankId = existedRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)

        val mockSavedRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        whenever(robotRepository.findById(mockId)).thenReturn(Optional.of(mockSavedRobotEntity))
        whenever(courseRepository.findById(mockCourseId)).thenReturn(Optional.of(mockCourseEntity))
        whenever(rankRepository.findById(mockRankId)).thenReturn(Optional.of(mockRankEntity))
        whenever(robotRepository.findByCourse_IdAndRank_Id(existedCourseId, existedRankId)).thenReturn(
            Optional.of(mockSavedRobotEntity),
        )
        whenever(robotRepository.save(mockSavedRobotEntity)).thenReturn(mockSavedRobotEntity)

        assertThrows(DuplicatedRobotException::class.java) { robotService.save(mockRobot) }
    }

    @Test
    internal fun `should return updated robot given new not existed courseId and rankId with existed id when save robot`() {
        val notDuplicatedCourseId = "ab88dd31-ba28-493e-90d5-b5a4d127d600"
        val notDuplicatedRankId = "ab88dd31-ba28-493e-90d5-b5a4d127d601"
        val mockRobot =
            Robot(
                id = mockId,
                courseId = notDuplicatedCourseId,
                rankId = notDuplicatedRankId,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        val mockCourseEntity =
            CourseEntity(
                id = mockCourseId,
                code = "code2",
                name = "name2",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )
        val mockRankEntity = RankEntity(id = mockRankId, name = "Easy", rankLevel = 0, star = 3)

        val mockSavedRobotEntity =
            RobotEntity(
                id = mockId,
                course = mockCourseEntity,
                rank = mockRankEntity,
                minAccuracy = 1,
                maxAccuracy = 5,
                minCompletionMinute = 1,
                minCompletionSecond = 30,
                maxCompletionMinute = 3,
                maxCompletionSecond = 50,
            )

        whenever(robotRepository.findById(mockId)).thenReturn(Optional.of(mockSavedRobotEntity))
        whenever(courseRepository.findById(notDuplicatedCourseId)).thenReturn(Optional.of(mockCourseEntity))
        whenever(rankRepository.findById(notDuplicatedRankId)).thenReturn(Optional.of(mockRankEntity))
        whenever(robotRepository.findByCourse_IdAndRank_Id(notDuplicatedCourseId, notDuplicatedRankId)).thenReturn(
            Optional.empty(),
        )
        whenever(robotRepository.save(mockSavedRobotEntity)).thenReturn(mockSavedRobotEntity)

        val savedRobot = robotService.save(mockRobot)

        assertEquals(savedRobot.id, mockSavedRobotEntity.id)
        assertEquals(savedRobot.courseId, mockSavedRobotEntity.course?.id)
        assertEquals(savedRobot.rankId, mockSavedRobotEntity.rank?.id)
        assertEquals(savedRobot.rankName, mockSavedRobotEntity.rank?.name)
        assertEquals(savedRobot.minAccuracy, mockSavedRobotEntity.minAccuracy)
        assertEquals(savedRobot.maxAccuracy, mockSavedRobotEntity.maxAccuracy)
        assertEquals(savedRobot.minCompletionMinute, mockSavedRobotEntity.minCompletionMinute)
        assertEquals(savedRobot.minCompletionSecond, mockSavedRobotEntity.minCompletionSecond)
        assertEquals(savedRobot.maxCompletionMinute, mockSavedRobotEntity.maxCompletionMinute)
        assertEquals(savedRobot.maxCompletionMinute, mockSavedRobotEntity.maxCompletionMinute)
        assertEquals(savedRobot.maxCompletionSecond, mockSavedRobotEntity.maxCompletionSecond)
    }
}

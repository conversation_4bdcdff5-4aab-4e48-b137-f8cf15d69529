package com.firstedu.marsladder.falcon.practice.controller

import com.firstedu.marsladder.falcon.practice.service.PracticeService
import com.firstedu.marsladder.falcon.practice.service.exception.InvalidPracticeException
import com.firstedu.marsladder.falcon.practice.service.exception.StudentTaskNotFoundException
import com.firstedu.marsladder.falcon.practice.service.exception.StudentTaskPracticeNotFoundException
import com.firstedu.marsladder.falcon.security.FalconJwtAuthenticationTokenConverter
import com.firstedu.marsladder.falcon.security.WebSecurityConfig
import com.firstedu.marsladder.falcon.user.WebMvcTestConfiguration
import com.nhaarman.mockitokotlin2.doNothing
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@WebMvcTest(
    controllers = [SchedulingPracticeController::class],
    excludeFilters = [
        ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = [
                FalconJwtAuthenticationTokenConverter::class,
            ],
        ),
    ],
)
@Import(WebSecurityConfig::class, WebMvcTestConfiguration::class)
internal class SchedulingPracticeControllerTest
    @Autowired
    private constructor(
        private val mockMvc: MockMvc,
    ) {
        @MockBean
        private lateinit var practiceService: PracticeService

        private val mockPracticeId = "practice id"

        @Test
        fun `should auto submit practice`() {
            doNothing().whenever(practiceService).autoSubmitPractice(mockPracticeId)

            mockMvc.perform(
                post("/scheduling-practices/{id}/auto-submit", mockPracticeId)
                    .contentType(MediaType.APPLICATION_JSON),
            ).andExpect(MockMvcResultMatchers.status().isOk)
        }

        @Test
        fun `should respond 400 for InvalidPracticeException`() {
            whenever(practiceService.autoSubmitPractice(mockPracticeId)).thenThrow(InvalidPracticeException("invalid practice"))

            mockMvc.perform(
                post("/scheduling-practices/{id}/auto-submit", mockPracticeId)
                    .contentType(MediaType.APPLICATION_JSON),
            ).andExpect(MockMvcResultMatchers.status().isBadRequest)
        }

        @Test
        fun `should respond 404 for StudentTaskPracticeNotFoundException`() {
            whenever(practiceService.autoSubmitPractice(mockPracticeId)).thenThrow(StudentTaskPracticeNotFoundException("student task practice not found"))

            mockMvc.perform(
                post("/scheduling-practices/{id}/auto-submit", mockPracticeId)
                    .contentType(MediaType.APPLICATION_JSON),
            ).andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        fun `should respond 404 for StudentTaskNotFoundException`() {
            whenever(practiceService.autoSubmitPractice(mockPracticeId)).thenThrow(StudentTaskNotFoundException("student task not found"))

            mockMvc.perform(
                post("/scheduling-practices/{id}/auto-submit", mockPracticeId)
                    .contentType(MediaType.APPLICATION_JSON),
            ).andExpect(MockMvcResultMatchers.status().isNotFound)
        }
    }

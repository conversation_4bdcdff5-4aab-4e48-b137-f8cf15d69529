package com.firstedu.marsladder.falcon.classroom.service.impl

import com.firstedu.marsladder.falcon.classroom.AcademicLevel
import com.firstedu.marsladder.falcon.classroom.ClassroomStatus
import com.firstedu.marsladder.falcon.classroom.ClassroomStudentStatus
import com.firstedu.marsladder.falcon.classroom.ClassroomType
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomRepository
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomStudentDetailRepository
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomStudentRepository
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomEntity
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomStudentDetailEntity
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomStudentEntity
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomStudent
import com.firstedu.marsladder.falcon.classroom.service.exception.AddStudentNoPermissionException
import com.firstedu.marsladder.falcon.classroom.service.exception.ClassroomNotFoundException
import com.firstedu.marsladder.falcon.classroom.service.exception.ClassroomStudentNotFoundException
import com.firstedu.marsladder.falcon.classroom.service.exception.DuplicateEmailException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidStudentAliasException
import com.firstedu.marsladder.falcon.classroom.service.exception.UpdateDismissedClassroomException
import com.firstedu.marsladder.falcon.config.S3FileProperties
import com.firstedu.marsladder.falcon.config.S3FileSizeProperties
import com.firstedu.marsladder.falcon.config.S3PathAndDefaultProperties
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.config.S3StaticProperties
import com.firstedu.marsladder.falcon.marketing.event.MarketingEvent
import com.firstedu.marsladder.falcon.marketing.event.MarketingEventPublisher
import com.firstedu.marsladder.falcon.marketing.event.MarketingEventType
import com.firstedu.marsladder.falcon.notification.NotificationFirstLevelType
import com.firstedu.marsladder.falcon.notification.NotificationSecondLevelType
import com.firstedu.marsladder.falcon.notification.NotificationStatus
import com.firstedu.marsladder.falcon.notification.repository.NotificationRepository
import com.firstedu.marsladder.falcon.notification.repository.entity.NotificationEntity
import com.firstedu.marsladder.falcon.profile.repository.ProfileRepository
import com.firstedu.marsladder.falcon.profile.repository.entity.ProfileEntity
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherPermission
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherPermissionService
import com.firstedu.marsladder.falcon.school.teacherprofile.service.TeacherProfileService
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.task.service.TaskStatisticsService
import com.firstedu.marsladder.falcon.tutor.tutorprofile.service.TutorProfileService
import com.firstedu.marsladder.falcon.tutor1.service.SeatService
import com.firstedu.marsladder.falcon.tutor1.service.domain.Seat
import com.firstedu.marsladder.falcon.tutor1.service.domain.SeatCapacity
import com.firstedu.marsladder.falcon.tutor1.service.domain.SeatInfo
import com.firstedu.marsladder.falcon.user.consumer.ConsumerService
import com.firstedu.marsladder.falcon.user.consumer.ConsumerType
import com.firstedu.marsladder.falcon.user.identity.repository.UserRepository
import com.firstedu.marsladder.falcon.user.identity.repository.UserRepositoryEntity
import com.firstedu.marsladder.falcon.utils.DateTimeUtil
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.Optional

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
internal class ClassroomStudentServiceImplTest {
    @Mock
    private lateinit var classroomRepository: ClassroomRepository

    @Mock
    private lateinit var classroomStudentRepository: ClassroomStudentRepository

    @Mock
    private lateinit var notificationRepository: NotificationRepository

    @Mock
    private lateinit var profileRepository: ProfileRepository

    @Mock
    private lateinit var userRepository: UserRepository

    @Mock
    private lateinit var taskStatisticsService: TaskStatisticsService

    @Mock
    private lateinit var sessionProvider: SessionProvider

    @Mock
    private lateinit var consumerService: ConsumerService

    @Mock
    private lateinit var teacherProfileService: TeacherProfileService

    @Mock
    private lateinit var teacherPermissionService: TeacherPermissionService

    @Mock
    private lateinit var tutorProfileService: TutorProfileService

    @Mock
    private lateinit var seatService: SeatService

    @Mock
    private lateinit var marketingEventPublisher: MarketingEventPublisher

    @Mock
    private lateinit var classroomStudentDetailRepository: ClassroomStudentDetailRepository

    @Mock
    private lateinit var s3Properties: S3Properties

    @Mock
    private lateinit var dateTimeUtil: DateTimeUtil

    @InjectMocks
    private lateinit var classroomStudentServiceImpl: ClassroomStudentServiceImpl

    private val mockStudentCognitoUid = "mock-student-cognito-uid"
    private val mockStudentAvatarUrl = "default.png"
    private val mockClassroomId = "mock-classroom-id"
    private val staticUrl = "https://static.test.marsladder.com.au"
    private val avatarPath = "avatars"
    private val mockUserId = "fakeTeacherCognitoUid"

    private val classroomTeacherEntity =
        ClassroomEntity(
            id = mockClassroomId,
            name = "fakeName",
            subjectId = "fakeSubjectId",
            courseId = "fakeCourseId",
            status = ClassroomStatus.IN_PROGRESS,
            label = "fakeLabel",
            teacherCognitoUid = "fakeTeacherCognitoUid",
            description = "fakeDescription",
            emptyNotified = false,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            type = ClassroomType.TEACHER,
        )

    private val classroomTutorEntity =
        ClassroomEntity(
            id = mockClassroomId,
            name = "fakeName",
            subjectId = "fakeSubjectId",
            courseId = "fakeCourseId",
            status = ClassroomStatus.IN_PROGRESS,
            label = "fakeLabel",
            teacherCognitoUid = "fakeTeacherCognitoUid",
            description = "fakeDescription",
            emptyNotified = false,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            type = ClassroomType.TUTOR,
        )

    private val dismissedClassroomEntity =
        ClassroomEntity(
            id = mockClassroomId,
            name = "fakeName",
            subjectId = "fakeSubjectId",
            courseId = "fakeCourseId",
            status = ClassroomStatus.DISMISSED,
            label = "fakeLabel",
            teacherCognitoUid = "fakeTeacherCognitoUid",
            description = "fakeDescription",
            emptyNotified = false,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            type = ClassroomType.TEACHER,
        )

    private val classroomStudentEntity =
        ClassroomStudentEntity(
            id = "fakeClassroomStudentId",
            classroomId = mockClassroomId,
            studentCognitoUid = mockStudentCognitoUid,
            studentAlias = "fakeStudentAlias",
            academicLevel = AcademicLevel.Excellent,
            suggestAcademicLevel = AcademicLevel.Good,
            status = ClassroomStudentStatus.IN,
            entryExitTimes = mutableListOf(LocalDateTime.of(2024, 2, 1, 1, 1, 1)),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )

    private val classroomStudentDetailEntity =
        ClassroomStudentDetailEntity(
            id = "fakeClassroomStudentId",
            classroomId = mockClassroomId,
            studentCognitoUid = mockStudentCognitoUid,
            studentAlias = "fakeStudentAlias",
            academicLevel = AcademicLevel.Excellent,
            suggestAcademicLevel = AcademicLevel.Good,
            status = ClassroomStudentStatus.IN,
            studentEmail = "<EMAIL>",
            studentLevel = 2,
            studentAvatar = "default.png",
            profileNickname = "fakeProfileNickname",
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )

    private val mockNotificationEntity =
        NotificationEntity(
            null,
            "message",
            "fakeStudentCognitoUid",
            NotificationStatus.NEW,
            NotificationFirstLevelType.CLASS,
            NotificationSecondLevelType.DAILY_TASK,
            LocalDateTime.of(2022, 6, 13, 22, 2, 6),
        )

    private val mockProfileEntity =
        ProfileEntity(
            id = "fakeId",
            cognitoUid = mockStudentCognitoUid,
            nickname = "fakeNickName",
            avatar = "default.png",
            gold = 1,
            experience = 1,
            level = 2,
        )

    private val mockUser = UserRepositoryEntity(mockStudentCognitoUid, "<EMAIL>", listOf())

    @BeforeEach
    internal fun setUp() {
        whenever(sessionProvider.getUserId()).thenReturn(mockUserId)
        whenever(dateTimeUtil.getNowTime())
            .thenReturn(LocalDateTime.of(2024, 1, 1, 1, 1, 1))
    }

    @Test
    internal fun `should return all students`() {
        val expectedClassroomStudent =
            ClassroomStudent(
                id = "fakeClassroomStudentId",
                mockClassroomId,
                studentUserId = mockStudentCognitoUid,
                studentAlias = "fakeStudentAlias",
                studentAvatarUrl = "$staticUrl/$avatarPath/$mockStudentAvatarUrl",
                status = ClassroomStudentStatus.IN,
                createdAt = classroomStudentDetailEntity.createdAt,
                updatedAt = classroomStudentDetailEntity.updatedAt,
                studentEmail = "<EMAIL>",
                academicLevel = AcademicLevel.Excellent,
                suggestAcademicLevel = classroomStudentEntity.suggestAcademicLevel,
                level = 2,
                profileNickname = "fakeProfileNickname",
            )

        whenever(s3Properties.static).thenReturn(S3StaticProperties("static.test.marsladder.com.au", "static", "https://static.test.marsladder.com.au", S3FileProperties(S3FileSizeProperties())))
        whenever(s3Properties.avatar).thenReturn(S3PathAndDefaultProperties("avatars", "default.png"))
        whenever(
            classroomStudentDetailRepository.findByClassroomIdAndStatus(
                eq(mockClassroomId),
                eq(ClassroomStudentStatus.IN),
            ),
        ).thenReturn(listOf(classroomStudentDetailEntity))

        val students = classroomStudentServiceImpl.getStudents(mockClassroomId)
        assertEquals(expectedClassroomStudent, students[0])
    }

    @Test
    internal fun `should return replace empty alias to nick name`() {
        val studentEntity =
            ClassroomStudentDetailEntity(
                id = "fakeClassroomStudentId",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                status = ClassroomStudentStatus.IN,
                academicLevel = AcademicLevel.Excellent,
                suggestAcademicLevel = AcademicLevel.Good,
                studentEmail = "<EMAIL>",
                studentAvatar = mockStudentAvatarUrl,
                profileNickname = "fakeNickName",
                studentLevel = 2,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
            )
        val expectedClassroomStudent =
            ClassroomStudent(
                id = "fakeClassroomStudentId",
                mockClassroomId,
                studentUserId = mockStudentCognitoUid,
                studentAlias = "fakeNickName",
                studentAvatarUrl = "$staticUrl/$avatarPath/$mockStudentAvatarUrl",
                status = ClassroomStudentStatus.IN,
                createdAt = studentEntity.createdAt,
                updatedAt = studentEntity.updatedAt,
                level = 2,
                studentEmail = "<EMAIL>",
                academicLevel = AcademicLevel.Excellent,
                suggestAcademicLevel = AcademicLevel.Good,
                profileNickname = "fakeNickName",
            )

        whenever(s3Properties.static).thenReturn(S3StaticProperties("static.test.marsladder.com.au", "static", "https://static.test.marsladder.com.au", S3FileProperties(S3FileSizeProperties())))
        whenever(s3Properties.avatar).thenReturn(S3PathAndDefaultProperties("avatars", "default.png"))
        whenever(
            classroomStudentDetailRepository.findByClassroomIdAndStatus(
                eq(mockClassroomId),
                eq(ClassroomStudentStatus.IN),
            ),
        ).thenReturn(listOf(studentEntity))

        val students = classroomStudentServiceImpl.getStudents(mockClassroomId)
        assertEquals(expectedClassroomStudent, students[0])
    }

    @Test
    internal fun `should add students successfully and send the marketing event given user is teacher and classroom type is TEACHER when add students`() {
        val emails = listOf("<EMAIL>", "<EMAIL>")
        val mockUser1 = UserRepositoryEntity("mockStudentCognitoUid1", "<EMAIL>", listOf())
        val expectedEntityList =
            listOf(
                ClassroomStudentEntity(
                    classroomId = mockClassroomId,
                    studentCognitoUid = mockStudentCognitoUid,
                    status = ClassroomStudentStatus.IN,
                    academicLevel = AcademicLevel.Exploring,
                    entryExitTimes = mutableListOf(LocalDateTime.of(2024, 1, 1, 1, 1, 1)),
                ),
                ClassroomStudentEntity(
                    classroomId = mockClassroomId,
                    studentCognitoUid = "mockStudentCognitoUid1",
                    status = ClassroomStudentStatus.IN,
                    academicLevel = AcademicLevel.Exploring,
                    entryExitTimes = mutableListOf(LocalDateTime.of(2024, 1, 1, 1, 1, 1)),
                ),
            )

        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(teacherPermissionService.getTeacherPermissions(mockUserId)).thenReturn(emptyList())

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq(mockStudentCognitoUid),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq("mockStudentCognitoUid1"),
            ),
        ).thenReturn(Optional.empty())
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser)
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser1)
        whenever(notificationRepository.save(any<NotificationEntity>())).thenReturn(mockNotificationEntity)
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(2, addStudentResult.successCount)
        assertEquals(0, addStudentResult.failedEmails.size)
        assertEquals(0, addStudentResult.existedEmails.size)
        verify(classroomStudentRepository, times(1)).saveAll(eq(expectedEntityList))
        verify(marketingEventPublisher, times(1)).publish(
            MarketingEvent(
                mockUserId,
                MarketingEventType.INVITE_STUDENTS,
                BigDecimal.valueOf(addStudentResult.successCount.toLong()),
            ),
        )
    }

    @Test
    internal fun `should throw no permission exception given user is not teacher and classroom type is TEACHER when add students`() {
        val emails = listOf("<EMAIL>", "<EMAIL>")

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(false)

        Assertions.assertThrows(AddStudentNoPermissionException::class.java) {
            classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)
        }
    }

    @Test
    internal fun `should add students successfully and send the marketing event given user is tutor has enough seats and classroom type is TUTOR when add students`() {
        val emails = listOf("<EMAIL>", "<EMAIL>")
        val mockUser1 = UserRepositoryEntity("mockStudentCognitoUid1", "<EMAIL>", listOf())
        val expectedEntityList =
            listOf(
                ClassroomStudentEntity(
                    classroomId = mockClassroomId,
                    studentCognitoUid = mockStudentCognitoUid,
                    status = ClassroomStudentStatus.IN,
                    academicLevel = AcademicLevel.Exploring,
                    entryExitTimes = mutableListOf(LocalDateTime.of(2024, 1, 1, 1, 1, 1)),
                ),
                ClassroomStudentEntity(
                    classroomId = mockClassroomId,
                    studentCognitoUid = "mockStudentCognitoUid1",
                    status = ClassroomStudentStatus.IN,
                    academicLevel = AcademicLevel.Exploring,
                    entryExitTimes = mutableListOf(LocalDateTime.of(2024, 1, 1, 1, 1, 1)),
                ),
            )
        val mockSeatInfo =
            SeatInfo(
                used = 0,
                capacity =
                    SeatCapacity(
                        current =
                            Seat(
                                id = "mock id",
                                userId = mockUserId,
                                capacity = 100,
                                startAt = 0,
                                endAt = 0,
                            ),
                        next = null,
                    ),
            )

        whenever(tutorProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(seatService.findSeatsByTutor(mockUserId)).thenReturn(mockSeatInfo)
        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTutorEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq(mockStudentCognitoUid),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq("mockStudentCognitoUid1"),
            ),
        ).thenReturn(Optional.empty())
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser)
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser1)
        whenever(notificationRepository.save(any<NotificationEntity>())).thenReturn(mockNotificationEntity)
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(2, addStudentResult.successCount)
        assertEquals(0, addStudentResult.failedEmails.size)
        assertEquals(0, addStudentResult.existedEmails.size)
        verify(classroomStudentRepository, times(1)).saveAll(eq(expectedEntityList))
        verify(marketingEventPublisher, times(1)).publish(
            MarketingEvent(
                mockUserId,
                MarketingEventType.INVITE_STUDENTS,
                BigDecimal.valueOf(addStudentResult.successCount.toLong()),
            ),
        )
    }

    @Test
    internal fun `should throw no permission exception given user is not tutor and classroom type is TUTOR when add students`() {
        val emails = listOf("<EMAIL>", "<EMAIL>")

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTutorEntity))
        whenever(tutorProfileService.isProfileExisted(mockUserId)).thenReturn(false)

        Assertions.assertThrows(AddStudentNoPermissionException::class.java) {
            classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)
        }
    }

    @Test
    internal fun `should throw no permission exception given user is tutor with no enough seats and classroom type is TUTOR when add students`() {
        val emails = listOf("<EMAIL>", "<EMAIL>")
        val mockSeatInfo =
            SeatInfo(
                used = 100,
                capacity =
                    SeatCapacity(
                        current =
                            Seat(
                                id = "mock id",
                                userId = mockUserId,
                                capacity = 100,
                                startAt = 0,
                                endAt = 0,
                            ),
                        next = null,
                    ),
            )
        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTutorEntity))
        whenever(tutorProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(seatService.findSeatsByTutor(mockUserId)).thenReturn(mockSeatInfo)

        Assertions.assertThrows(AddStudentNoPermissionException::class.java) {
            classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)
        }
    }

    @Test
    internal fun `should add all students when student email matches given suffix`() {
        val emails =
            listOf(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            )

        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(teacherPermissionService.getTeacherPermissions(mockUserId)).thenReturn(emptyList())

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq(mockStudentCognitoUid),
            ),
        ).thenReturn(Optional.empty())
        whenever(userRepository.findFirstByEmail(any())).thenReturn(mockUser)
        whenever(notificationRepository.save(any<NotificationEntity>())).thenReturn(mockNotificationEntity)
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(6, addStudentResult.successCount)
        assertEquals(3, addStudentResult.failedEmails.size)
        assertEquals(0, addStudentResult.existedEmails.size)
        verify(classroomStudentRepository, times(1)).saveAll(any<List<ClassroomStudentEntity>>())
    }

    @Test
    internal fun `should not add students with postfix other than predefined suffix when teacher is not from marsladder`() {
        val emails =
            listOf(
                "<EMAIL>",
            )

        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(sessionProvider.getUserId()).thenReturn(mockUserId)
        whenever(tutorProfileService.isProfileExisted(mockUserId)).thenReturn(false)
        whenever(teacherPermissionService.getTeacherPermissions(mockUserId)).thenReturn(emptyList())
        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(0, addStudentResult.successCount)
        assertEquals(1, addStudentResult.failedEmails.size)
        assertEquals(0, addStudentResult.existedEmails.size)
        verify(classroomStudentRepository, never()).saveAll(any<List<ClassroomStudentEntity>>())
    }

    @Test
    internal fun `should ignore existed when adding students`() {
        val emails = listOf("<EMAIL>")
        val classroomStudentEntity =
            ClassroomStudentEntity(
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                status = ClassroomStudentStatus.IN,
                academicLevel = AcademicLevel.Excellent,
                entryExitTimes = mutableListOf(LocalDateTime.now()),
            )
        val expectedEntityList = listOf<ClassroomStudentEntity>()

        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(teacherPermissionService.getTeacherPermissions(mockUserId)).thenReturn(emptyList())

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq(mockStudentCognitoUid),
            ),
        ).thenReturn(Optional.of(classroomStudentEntity))
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser)
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(0, addStudentResult.successCount)
        assertEquals(0, addStudentResult.failedEmails.size)
        assertEquals("<EMAIL>", addStudentResult.existedEmails[0])
        verify(classroomStudentRepository, times(0)).saveAll(eq(expectedEntityList))
    }

    @Test
    internal fun `should update status from OUT to IN when adding moved out students`() {
        val emails = listOf("<EMAIL>")
        val now = LocalDateTime.now()
        val classroomStudentEntity =
            ClassroomStudentEntity(
                id = "id",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                status = ClassroomStudentStatus.OUT,
                studentAlias = "alias",
                academicLevel = AcademicLevel.Excellent,
                entryExitTimes = mutableListOf(LocalDateTime.of(2024, 2, 1, 1, 1, 1)),
                createdAt = now,
                updatedAt = now,
            )
        val expectedEntityList =
            listOf(
                ClassroomStudentEntity(
                    id = "id",
                    classroomId = mockClassroomId,
                    studentCognitoUid = mockStudentCognitoUid,
                    status = ClassroomStudentStatus.IN,
                    academicLevel = AcademicLevel.Excellent,
                    studentAlias = "alias",
                    entryExitTimes = mutableListOf(LocalDateTime.of(2024, 2, 1, 1, 1, 1), LocalDateTime.of(2024, 1, 1, 1, 1, 1)),
                    createdAt = now,
                    updatedAt = now,
                ),
            )

        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(teacherPermissionService.getTeacherPermissions(mockUserId)).thenReturn(emptyList())
        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq(mockStudentCognitoUid),
            ),
        ).thenReturn(Optional.of(classroomStudentEntity))
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser)
        whenever(notificationRepository.save(any<NotificationEntity>())).thenReturn(mockNotificationEntity)
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(1, addStudentResult.successCount)
        assertEquals(0, addStudentResult.failedEmails.size)
        assertEquals(0, addStudentResult.existedEmails.size)
        verify(classroomStudentRepository, times(1)).saveAll(eq(expectedEntityList))
    }

    @Test
    internal fun `should throw exception when adding student to non-exist classroom`() {
        val emails = listOf("email1", "email2")

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.empty())

        Assertions.assertThrows(ClassroomNotFoundException::class.java) {
            classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)
        }
    }

    @Test
    internal fun `should throw exception when adding student to dismissed classroom`() {
        val emails = listOf("email1", "email2")

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(dismissedClassroomEntity))

        Assertions.assertThrows(UpdateDismissedClassroomException::class.java) {
            classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)
        }
    }

    @Test
    internal fun `should add student when adding student with non-exist user`() {
        val emails = listOf("<EMAIL>", "<EMAIL>")

        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(teacherPermissionService.getTeacherPermissions(mockUserId)).thenReturn(emptyList())
        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser)
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(null)
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")
        whenever(
            consumerService.createConsumer(
                eq("email2"),
                eq("<EMAIL>"),
                eq(ConsumerType.CLASS_STUDENT),
            ),
        ).thenReturn("newStudentId")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(2, addStudentResult.successCount)
    }

    @Test
    internal fun `should add all students when teacher has permission to add any email`() {
        val emails = listOf("<EMAIL>")
        val expectedEntityList =
            listOf(
                ClassroomStudentEntity(
                    classroomId = mockClassroomId,
                    studentCognitoUid = mockStudentCognitoUid,
                    status = ClassroomStudentStatus.IN,
                    academicLevel = AcademicLevel.Exploring,
                    entryExitTimes = mutableListOf(LocalDateTime.of(2024, 1, 1, 1, 1, 1)),
                ),
            )

        whenever(teacherProfileService.isProfileExisted(mockUserId)).thenReturn(true)
        whenever(teacherPermissionService.getTeacherPermissions(mockUserId)).thenReturn(TeacherPermission.entries)
        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                eq(mockClassroomId),
                eq(mockStudentCognitoUid),
            ),
        ).thenReturn(Optional.empty())
        whenever(userRepository.findFirstByEmail(eq("<EMAIL>"))).thenReturn(mockUser)
        whenever(notificationRepository.save(any<NotificationEntity>())).thenReturn(mockNotificationEntity)
        whenever(sessionProvider.getEmail()).thenReturn("<EMAIL>")

        val addStudentResult = classroomStudentServiceImpl.addStudents(mockUserId, mockClassroomId, emails)

        assertEquals(1, addStudentResult.successCount)
        assertEquals(0, addStudentResult.failedEmails.size)
        assertEquals(0, addStudentResult.existedEmails.size)
        verify(classroomStudentRepository, times(1)).saveAll(eq(expectedEntityList))
    }

    @Test
    internal fun `should success when moving student out from classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        val expectedEntity =
            ClassroomStudentEntity(
                id = "fakeClassroomStudentId",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                studentAlias = "fakeStudentAlias",
                academicLevel = AcademicLevel.Excellent,
                suggestAcademicLevel = AcademicLevel.Good,
                status = ClassroomStudentStatus.OUT,
                entryExitTimes = mutableListOf(LocalDateTime.of(2024, 2, 1, 1, 1, 1), LocalDateTime.of(2024, 1, 1, 1, 1, 1)),
                createdAt = classroomStudentEntity.createdAt,
                updatedAt = classroomStudentEntity.updatedAt,
            )

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(classroomStudentRepository.findById(classroomStudentId)).thenReturn(Optional.of(classroomStudentEntity))
        whenever(classroomStudentRepository.save(eq(expectedEntity))).thenReturn(expectedEntity)

        val updatedClassroomStudent = classroomStudentServiceImpl.moveOutStudent(mockClassroomId, classroomStudentId)

        assertEquals(ClassroomStudent.from(expectedEntity), updatedClassroomStudent)
    }

    @Test
    internal fun `should throw exception when moving student out with non-exist classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.empty())

        Assertions.assertThrows(ClassroomNotFoundException::class.java) {
            classroomStudentServiceImpl.moveOutStudent(mockClassroomId, classroomStudentId)
        }
    }

    @Test
    internal fun `should throw exception when moving student out with dismissed classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(dismissedClassroomEntity))

        Assertions.assertThrows(UpdateDismissedClassroomException::class.java) {
            classroomStudentServiceImpl.moveOutStudent(mockClassroomId, classroomStudentId)
        }
    }

    @Test
    internal fun `should throw exception when moving student out with non-exist classroom student`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(classroomStudentRepository.findById(classroomStudentId)).thenReturn(Optional.empty())

        Assertions.assertThrows(ClassroomStudentNotFoundException::class.java) {
            classroomStudentServiceImpl.moveOutStudent(mockClassroomId, classroomStudentId)
        }
    }

    @Test
    internal fun `should success when updating student alias from classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        val expectedEntity =
            ClassroomStudentEntity(
                id = "fakeClassroomStudentId",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                studentAlias = "newAlias",
                academicLevel = AcademicLevel.Excellent,
                suggestAcademicLevel = AcademicLevel.Good,
                status = ClassroomStudentStatus.IN,
                createdAt = classroomStudentEntity.createdAt,
                entryExitTimes = mutableListOf(LocalDateTime.of(2024, 2, 1, 1, 1, 1)),
                updatedAt = classroomStudentEntity.updatedAt,
            )

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(classroomStudentRepository.findById(classroomStudentId)).thenReturn(Optional.of(classroomStudentEntity))
        whenever(classroomStudentRepository.save(eq(expectedEntity))).thenReturn(expectedEntity)

        val updatedClassroomStudent =
            classroomStudentServiceImpl.updateStudentAlias(mockClassroomId, classroomStudentId, "newAlias")

        assertEquals(ClassroomStudent.from(expectedEntity), updatedClassroomStudent)
    }

    @Test
    internal fun `should throw exception when updating student alias to empty string`() {
        val classroomStudentId = "fakeClassroomStudentId"

        Assertions.assertThrows(InvalidStudentAliasException::class.java) {
            classroomStudentServiceImpl.updateStudentAlias(mockClassroomId, classroomStudentId, "")
        }
    }

    @Test
    internal fun `should throw exception when updating student alias with length great than 100`() {
        val classroomStudentId = "fakeClassroomStudentId"
        val aliceWith101Letters = "a".repeat(101)

        Assertions.assertThrows(InvalidStudentAliasException::class.java) {
            classroomStudentServiceImpl.updateStudentAlias(mockClassroomId, classroomStudentId, aliceWith101Letters)
        }
    }

    @Test
    internal fun `should throw exception when updating student alias with non-exist classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.empty())

        Assertions.assertThrows(ClassroomNotFoundException::class.java) {
            classroomStudentServiceImpl.updateStudentAlias(mockClassroomId, classroomStudentId, "newAlias")
        }
    }

    @Test
    internal fun `should throw exception when updating student alias with dismissed classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(dismissedClassroomEntity))

        Assertions.assertThrows(UpdateDismissedClassroomException::class.java) {
            classroomStudentServiceImpl.updateStudentAlias(mockClassroomId, classroomStudentId, "newAlias")
        }
    }

    @Test
    internal fun `should throw exception when updating student alias with non-exist classroom student`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(classroomStudentRepository.findById(classroomStudentId)).thenReturn(Optional.empty())

        Assertions.assertThrows(ClassroomStudentNotFoundException::class.java) {
            classroomStudentServiceImpl.updateStudentAlias(mockClassroomId, classroomStudentId, "newAlias")
        }
    }

    @Test
    internal fun `should success when updating student academic level`() {
        val expectedEntity =
            ClassroomStudentEntity(
                id = "fakeClassroomStudentId",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                studentAlias = "fakeStudentAlias",
                academicLevel = AcademicLevel.Exploring,
                suggestAcademicLevel = AcademicLevel.Good,
                status = ClassroomStudentStatus.IN,
                entryExitTimes = mutableListOf(LocalDateTime.of(2024, 2, 1, 1, 1, 1)),
                createdAt = classroomStudentEntity.createdAt,
                updatedAt = classroomStudentEntity.updatedAt,
            )

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                mockClassroomId,
                mockStudentCognitoUid,
            ),
        ).thenReturn(Optional.of(classroomStudentEntity))
        whenever(classroomStudentRepository.save(eq(expectedEntity))).thenReturn(expectedEntity)

        val updatedClassroomStudent =
            classroomStudentServiceImpl.updateAcademicLevel(
                mockClassroomId,
                mockStudentCognitoUid,
                AcademicLevel.Exploring,
            )

        assertEquals(ClassroomStudent.from(expectedEntity), updatedClassroomStudent)
    }

    @Test
    internal fun `should throw exception when updating student academic level with non-exist classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.empty())

        Assertions.assertThrows(ClassroomNotFoundException::class.java) {
            classroomStudentServiceImpl.updateAcademicLevel(
                mockClassroomId,
                classroomStudentId,
                AcademicLevel.Exploring,
            )
        }
    }

    @Test
    internal fun `should throw exception when updating student academic level with dismissed classroom`() {
        val classroomStudentId = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(dismissedClassroomEntity))

        Assertions.assertThrows(UpdateDismissedClassroomException::class.java) {
            classroomStudentServiceImpl.updateAcademicLevel(
                mockClassroomId,
                classroomStudentId,
                AcademicLevel.Exploring,
            )
        }
    }

    @Test
    internal fun `should throw exception when updating student academic level with non-exist classroom student`() {
        val classroomStudentCognitoUid = "fakeClassroomStudentId"

        whenever(classroomRepository.findById(eq(mockClassroomId))).thenReturn(Optional.of(classroomTeacherEntity))
        whenever(
            classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(
                mockClassroomId,
                classroomStudentCognitoUid,
            ),
        ).thenReturn(Optional.empty())

        Assertions.assertThrows(ClassroomStudentNotFoundException::class.java) {
            classroomStudentServiceImpl.updateAcademicLevel(
                mockClassroomId,
                classroomStudentCognitoUid,
                AcademicLevel.Exploring,
            )
        }
    }

    @Test
    internal fun `should return students with suggest academic level`() {
        val expectedClassroomStudent =
            ClassroomStudent(
                id = "fakeClassroomStudentId",
                mockClassroomId,
                studentUserId = mockStudentCognitoUid,
                studentAlias = "fakeStudentAlias",
                studentAvatarUrl = "$staticUrl/$avatarPath/default.png",
                status = ClassroomStudentStatus.IN,
                createdAt = classroomStudentDetailEntity.createdAt,
                updatedAt = classroomStudentDetailEntity.updatedAt,
                studentEmail = "<EMAIL>",
                academicLevel = AcademicLevel.Excellent,
                suggestAcademicLevel = AcademicLevel.Good,
                level = 2,
                profileNickname = "fakeProfileNickname",
            )

        whenever(s3Properties.static).thenReturn(S3StaticProperties("static.test.marsladder.com.au", "static", "https://static.test.marsladder.com.au", S3FileProperties(S3FileSizeProperties())))
        whenever(s3Properties.avatar).thenReturn(S3PathAndDefaultProperties("avatars", "default.png"))
        whenever(
            classroomStudentDetailRepository.findByClassroomIdAndStatus(
                eq(mockClassroomId),
                eq(ClassroomStudentStatus.IN),
            ),
        ).thenReturn(listOf(classroomStudentDetailEntity.copy(suggestAcademicLevel = AcademicLevel.Good)))

        val students = classroomStudentServiceImpl.getStudents(mockClassroomId)

        assertEquals(expectedClassroomStudent, students[0])
    }

    @Test
    internal fun `should return true when student entry two times`() {
        val classroomStudentEntity =
            ClassroomStudentEntity(
                id = "fakeClassroomStudentId",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                studentAlias = "fakeStudentAlias",
                academicLevel = AcademicLevel.Exploring,
                suggestAcademicLevel = AcademicLevel.Good,
                status = ClassroomStudentStatus.IN,
                entryExitTimes = mutableListOf(LocalDateTime.now().minusHours(12), LocalDateTime.now().minusHours(6), LocalDateTime.now().minusHours(1)),
                createdAt = classroomStudentEntity.createdAt,
                updatedAt = classroomStudentEntity.updatedAt,
            )
        whenever(classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(mockClassroomId, mockStudentCognitoUid))
            .thenReturn(Optional.of(classroomStudentEntity))

        val result = classroomStudentServiceImpl.isStudentInClassTimeRange(LocalDateTime.now().minusHours(8), mockStudentCognitoUid, mockClassroomId)

        assertEquals(result, true)
    }

    @Test
    internal fun `should return true when student entry one times`() {
        val classroomStudentEntity =
            ClassroomStudentEntity(
                id = "fakeClassroomStudentId",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                studentAlias = "fakeStudentAlias",
                academicLevel = AcademicLevel.Exploring,
                suggestAcademicLevel = AcademicLevel.Good,
                status = ClassroomStudentStatus.IN,
                entryExitTimes = mutableListOf(LocalDateTime.now().minusHours(12), LocalDateTime.now().minusHours(6)),
                createdAt = classroomStudentEntity.createdAt,
                updatedAt = classroomStudentEntity.updatedAt,
            )
        whenever(classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(mockClassroomId, mockStudentCognitoUid))
            .thenReturn(Optional.of(classroomStudentEntity))

        val result = classroomStudentServiceImpl.isStudentInClassTimeRange(LocalDateTime.now().minusHours(10), mockStudentCognitoUid, mockClassroomId)

        assertEquals(result, true)
    }

    @Test
    internal fun `should return false when student entry two times`() {
        val classroomStudentEntity =
            ClassroomStudentEntity(
                id = "fakeClassroomStudentId",
                classroomId = mockClassroomId,
                studentCognitoUid = mockStudentCognitoUid,
                studentAlias = "fakeStudentAlias",
                academicLevel = AcademicLevel.Exploring,
                suggestAcademicLevel = AcademicLevel.Good,
                status = ClassroomStudentStatus.IN,
                entryExitTimes = mutableListOf(LocalDateTime.now().minusHours(12), LocalDateTime.now().minusHours(6), LocalDateTime.now().minusHours(1)),
                createdAt = classroomStudentEntity.createdAt,
                updatedAt = classroomStudentEntity.updatedAt,
            )
        whenever(classroomStudentRepository.findByClassroomIdAndStudentCognitoUid(mockClassroomId, mockStudentCognitoUid))
            .thenReturn(Optional.of(classroomStudentEntity))

        val result = classroomStudentServiceImpl.isStudentInClassTimeRange(LocalDateTime.now().minusHours(4), mockStudentCognitoUid, mockClassroomId)

        assertEquals(result, false)
    }
}

package com.firstedu.marsladder.falcon.payment.controller

import com.firstedu.marsladder.falcon.payment.service.PaymentService
import com.firstedu.marsladder.falcon.payment.service.domain.StripeInvoice
import com.firstedu.marsladder.falcon.payment.service.domain.StripePayment
import com.firstedu.marsladder.falcon.payment.service.domain.StripePaymentMethod
import com.firstedu.marsladder.falcon.payment.service.domain.StripeTeacherPayment
import com.firstedu.marsladder.falcon.payment.service.exception.StripePaymentException
import com.firstedu.marsladder.falcon.payment.type.SubscriptionItemSource
import com.firstedu.marsladder.falcon.seatSubscription.service.exception.SubscribedSeatNotFoundException
import com.firstedu.marsladder.falcon.security.FalconJwtAuthenticationTokenConverter
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.subscription.service.exception.CustomerNotFoundException
import com.firstedu.marsladder.falcon.subscription.service.exception.StripeCustomerNotFoundException
import com.firstedu.marsladder.falcon.subscription.service.exception.SubscriptionNotFoundException
import com.firstedu.marsladder.falcon.subscription.type.SubscriptionFrequency
import com.firstedu.marsladder.falcon.user.WebMvcTestConfiguration
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.time.LocalDateTime

@WebMvcTest(
    PaymentController::class,
    excludeFilters = [
        ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = [
                FalconJwtAuthenticationTokenConverter::class,
            ],
        ),
    ],
)
@Import(WebMvcTestConfiguration::class)
internal class PaymentControllerTest
    @Autowired
    private constructor(
        private val mockMvc: MockMvc,
    ) {
        @MockBean
        private lateinit var paymentService: PaymentService

        @MockBean
        private lateinit var sessionProvider: SessionProvider

        private val mockCognitoUid = "cognito uid"
        private val mockPaymentId = "payment id"

        private val mockPaymentMethod =
            StripePaymentMethod(
                id = "payment method id",
                type = "card",
                brand = "brand",
                country = "AU",
                funding = "funding",
                last4 = "1234",
            )

        private val mockStripeInvoice =
            StripeInvoice(
                id = "id",
                number = "number",
                invoicePdf = "invoice pdf",
                receiptNumber = "receipt number",
                paid = true,
                subscriptionId = "subscription id",
                status = "status",
                hostedInvoiceUrl = "hosted invoice url",
                subtotal = "100",
                tax = "10",
            )

        private val mockPayment =
            StripePayment(
                id = "payment id",
                amount = "100.0",
                currency = "AUD",
                mockPaymentMethod,
                status = "succeed",
                created = LocalDateTime.of(2021, 11, 26, 23, 29, 57),
                invoice = mockStripeInvoice,
                source = SubscriptionItemSource.COURSE,
                nextBillingTime = LocalDateTime.of(2021, 12, 26, 23, 29, 57),
                seats = null,
                increasedSeats = null,
            )

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 200 with course payments`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.COURSE), eq("aud")),
            ).thenReturn(listOf(mockPayment))

            val expectedResponseBody =
                """
                [{"id":"payment id","amount":"100.0","currency":"AUD","paymentMethod":{"id":"payment method id","type":"card","brand":"brand","country":"AU","funding":"funding","last4":"1234"},"status":"succeed","created":"2021-11-26T23:29:57","nextBillingTime":"2021-12-26T23:29:57","invoice":{"id":"id","number":"number","invoicePdf":"invoice pdf","receiptNumber":"receipt number","paid":true,"subscriptionId":"subscription id","status":"status","hostedInvoiceUrl":"hosted invoice url","subtotal":"100","tax":"10"},"source":"COURSE","seats":null,"increasedSeats":null}]
                """.trimIndent()

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments?currency=aud").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk)
                .andExpect(MockMvcResultMatchers.content().string(expectedResponseBody))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when subscription not found`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.COURSE), eq("aud")),
            ).thenThrow(SubscriptionNotFoundException("subscription not found"))

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments?currency=aud").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 200 with seat payments`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.SEAT), eq("aud")),
            ).thenReturn(listOf(mockPayment.copy(source = SubscriptionItemSource.SEAT, seats = 5)))

            val expectedResponseBody =
                """
                [{"id":"payment id","amount":"100.0","currency":"AUD","paymentMethod":{"id":"payment method id","type":"card","brand":"brand","country":"AU","funding":"funding","last4":"1234"},"status":"succeed","created":"2021-11-26T23:29:57","nextBillingTime":"2021-12-26T23:29:57","invoice":{"id":"id","number":"number","invoicePdf":"invoice pdf","receiptNumber":"receipt number","paid":true,"subscriptionId":"subscription id","status":"status","hostedInvoiceUrl":"hosted invoice url","subtotal":"100","tax":"10"},"source":"SEAT","seats":5,"increasedSeats":null}]
                """.trimIndent()

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/seats?currency=aud").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk)
                .andExpect(MockMvcResultMatchers.content().string(expectedResponseBody))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["TEACHER"])
        internal fun `should return 200 with teacher payments`() {
            val mockTeacherPayment =
                StripeTeacherPayment(
                    id = "payment id",
                    amount = "100.0",
                    currency = "AUD",
                    SubscriptionFrequency.MONTHLY,
                    mockPaymentMethod,
                    status = "succeed",
                    created = LocalDateTime.of(2021, 11, 26, 23, 29, 57),
                    invoice = mockStripeInvoice,
                    nextBillingTime = LocalDateTime.of(2021, 12, 26, 23, 29, 57),
                )
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getTeacherPayments(eq(mockCognitoUid), eq("aud")),
            ).thenReturn(listOf(mockTeacherPayment))

            val expectedResponseBody =
                """
                [{"id":"payment id","amount":"100.0","currency":"AUD","frequency":"MONTHLY","paymentMethod":{"id":"payment method id","type":"card","brand":"brand","country":"AU","funding":"funding","last4":"1234"},"status":"succeed","created":"2021-11-26T23:29:57","nextBillingTime":"2021-12-26T23:29:57","invoice":{"id":"id","number":"number","invoicePdf":"invoice pdf","receiptNumber":"receipt number","paid":true,"subscriptionId":"subscription id","status":"status","hostedInvoiceUrl":"hosted invoice url","subtotal":"100","tax":"10"}}]
                """.trimIndent()

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/teachers?currency=aud").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk)
                .andExpect(MockMvcResultMatchers.content().string(expectedResponseBody))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when subscribed seat not found`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.SEAT), eq("aud")),
            ).thenThrow(SubscribedSeatNotFoundException("seat not found"))

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/seats?currency=aud").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when seat subscription not found`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.SEAT), eq("aud")),
            ).thenThrow(SubscriptionNotFoundException("subscription not found"))

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/seats?currency=aud").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when customer not found`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.SEAT), eq("aud")),
            ).thenThrow(CustomerNotFoundException("customer not found"))

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/seats?currency=aud").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 200 with payment`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(paymentService.getPayment(eq(mockCognitoUid), eq(mockPaymentId))).thenReturn(mockPayment)

            val expectedResponseBody =
                """
                {"id":"payment id","amount":"100.0","currency":"AUD","paymentMethod":{"id":"payment method id","type":"card","brand":"brand","country":"AU","funding":"funding","last4":"1234"},"status":"succeed","created":"2021-11-26T23:29:57","nextBillingTime":"2021-12-26T23:29:57","invoice":{"id":"id","number":"number","invoicePdf":"invoice pdf","receiptNumber":"receipt number","paid":true,"subscriptionId":"subscription id","status":"status","hostedInvoiceUrl":"hosted invoice url","subtotal":"100","tax":"10"},"source":"COURSE","seats":null,"increasedSeats":null}
                """.trimIndent()

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/$mockPaymentId").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk)
                .andExpect(MockMvcResultMatchers.content().string(expectedResponseBody))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when no subscribed seat`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayment(eq(mockCognitoUid), eq(mockPaymentId)),
            ).thenThrow(SubscribedSeatNotFoundException("subscribed seat not found"))

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/$mockPaymentId").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when no subscription`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayment(eq(mockCognitoUid), eq(mockPaymentId)),
            ).thenThrow(SubscriptionNotFoundException("subscription not found"))

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/$mockPaymentId").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when no customer`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayment(eq(mockCognitoUid), eq(mockPaymentId)),
            ).thenThrow(CustomerNotFoundException("customer not found"))

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/$mockPaymentId").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 200 with payment methods`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(paymentService.getPaymentMethods(eq(mockCognitoUid))).thenReturn(listOf(mockPaymentMethod))

            val expectedResponseBody =
                """
                [{"id":"payment method id","type":"card","brand":"brand","country":"AU","funding":"funding","last4":"1234"}]
                """.trimIndent()

            mockMvc
                .perform(MockMvcRequestBuilders.get("/payments/methods").accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk)
                .andExpect(MockMvcResultMatchers.content().string(expectedResponseBody))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 400 when get course payments from stripe failed`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.COURSE), eq("aud")),
            ).thenThrow(StripePaymentException("get stripe payments failed"))

            mockMvc
                .perform(
                    MockMvcRequestBuilders.get("/payments?currency=aud")
                        .accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(MockMvcResultMatchers.status().isBadRequest)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 400 when get seat payments from stripe failed`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayments(eq(mockCognitoUid), eq(SubscriptionItemSource.SEAT), eq("aud")),
            ).thenThrow(StripePaymentException("get stripe payments failed"))

            mockMvc
                .perform(
                    MockMvcRequestBuilders.get("/payments/seats?currency=aud")
                        .accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(MockMvcResultMatchers.status().isBadRequest)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 400 when get payment from stripe failed`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(
                paymentService.getPayment(
                    eq(mockCognitoUid),
                    eq(mockPaymentId),
                ),
            ).thenThrow(StripePaymentException("get stripe payment failed"))

            mockMvc
                .perform(
                    MockMvcRequestBuilders.get("/payments/$mockPaymentId")
                        .accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(MockMvcResultMatchers.status().isBadRequest)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when get payment failed when no customer in db`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(paymentService.getPayment(eq(mockCognitoUid), eq(mockPaymentId))).thenThrow(
                StripeCustomerNotFoundException("get stripe customer failed"),
            )

            mockMvc
                .perform(
                    MockMvcRequestBuilders.get("/payments/$mockPaymentId")
                        .accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(MockMvcResultMatchers.status().isNotFound)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 400 when get payment methods from stripe failed`() {
            whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
            whenever(paymentService.getPaymentMethods(eq(mockCognitoUid))).thenThrow(StripePaymentException("get stripe payments failed"))

            mockMvc
                .perform(
                    MockMvcRequestBuilders.get("/payments/methods")
                        .accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(MockMvcResultMatchers.status().isBadRequest)
        }
    }

package com.firstedu.marsladder.falcon.question.service.impl

import com.firstedu.marsladder.falcon.practice.PracticeMode
import com.firstedu.marsladder.falcon.practice.PracticePlayerType
import com.firstedu.marsladder.falcon.practice.PracticeResultStatus
import com.firstedu.marsladder.falcon.practice.PracticeStatus
import com.firstedu.marsladder.falcon.practice.repository.PracticeRepository
import com.firstedu.marsladder.falcon.practice.repository.entity.Answer
import com.firstedu.marsladder.falcon.practice.repository.entity.MetaData
import com.firstedu.marsladder.falcon.practice.repository.entity.Player
import com.firstedu.marsladder.falcon.practice.repository.entity.PracticeEntity
import com.firstedu.marsladder.falcon.practice.repository.entity.Question
import com.firstedu.marsladder.falcon.question.QuestionCAS
import com.firstedu.marsladder.falcon.question.QuestionDifficulty
import com.firstedu.marsladder.falcon.question.repository.PracticeSyncHistoryRepository
import com.firstedu.marsladder.falcon.question.repository.QuestionPracticeDetailRepository
import com.firstedu.marsladder.falcon.question.repository.entity.PracticeSyncHistoryEntity
import com.firstedu.marsladder.falcon.question.repository.entity.QuestionPracticeDetailEntity
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentMatchers.anyList
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockitoExtension::class)
class QuestionPracticeDetailServiceImplTest {
    private lateinit var questionPracticeDetailServiceImpl: QuestionPracticeDetailServiceImpl

    @Mock
    lateinit var questionPracticeDetailRepository: QuestionPracticeDetailRepository

    @Mock
    private lateinit var practiceSyncHistoryRepository: PracticeSyncHistoryRepository

    @Mock
    private lateinit var practiceRepository: PracticeRepository

    @BeforeEach
    internal fun setUp() {
        questionPracticeDetailServiceImpl =
            QuestionPracticeDetailServiceImpl(
                questionPracticeDetailRepository,
                practiceSyncHistoryRepository,
                practiceRepository,
            )
    }

    @Test
    fun `save nothing when find practice record  with empty list`() {
        val currentTime = LocalDateTime.now().withNano(0)
        `when`(practiceSyncHistoryRepository.findFirstByOrderByPracticeEndAtDesc()).thenReturn(
            PracticeSyncHistoryEntity("1", currentTime),
        )
        `when`(practiceRepository.findByStatusAndEndAtGreaterThanAndEndAtLessThan(any(), any(), any())).thenReturn(emptyList())
        questionPracticeDetailServiceImpl.syncQuestionPracticeDetailFromPractice()
        verify(questionPracticeDetailRepository, never()).saveAll(anyList())
    }

    @Test
    fun `save all entity when find practice record`() {
        val practiceId = UUID.randomUUID().toString()
        val questionId = UUID.randomUUID().toString()
        val courseId = UUID.randomUUID().toString()
        val studentId = UUID.randomUUID().toString()
        val playId = UUID.randomUUID().toString()
        val originalQuestionId = UUID.randomUUID().toString()
        val startAt = LocalDateTime.now().minusHours(1)
        val endAt = LocalDateTime.now()
        val play = Player(playId, studentId, null, null, PracticePlayerType.STUDENT, 0, PracticeResultStatus.SUBMITTED, 0, 0, 0, null, mutableListOf(Answer(questionId, "optionId", true)))

        val practiceEntity =
            PracticeEntity(
                practiceId,
                courseId,
                PracticeMode.CHALLENGE,
                PracticeStatus.FINISHED,
                startAt, endAt, null,
                listOf(Question(questionId, null, originalQuestionId, 0, "topic", "answerAnalysis", emptyList(), QuestionDifficulty.EASY, QuestionCAS.FREE)),
                MetaData("", false, emptyList(), null, null, null, null, null),
                listOf(play),
            )

        val practiceList = mutableListOf(practiceEntity)

        val practiceDetailEntityList: List<QuestionPracticeDetailEntity> = listOf(QuestionPracticeDetailEntity("", originalQuestionId, courseId, practiceId, 1, 1, -3601, -3601, studentId))
        `when`(practiceSyncHistoryRepository.findFirstByOrderByPracticeEndAtDesc()).thenReturn(PracticeSyncHistoryEntity("", LocalDateTime.now()))
        `when`(practiceRepository.findByStatusAndEndAtGreaterThanAndEndAtLessThan(any(), any(), any())).thenReturn(practiceList)

        questionPracticeDetailServiceImpl.syncQuestionPracticeDetailFromPractice()
        verify(practiceSyncHistoryRepository, times(1)).findFirstByOrderByPracticeEndAtDesc()
        verify(practiceRepository, times(1)).findByStatusAndEndAtGreaterThanAndEndAtLessThan(any(), any(), any())
        verify(questionPracticeDetailRepository, times(1)).saveAll(practiceDetailEntityList)
    }
}

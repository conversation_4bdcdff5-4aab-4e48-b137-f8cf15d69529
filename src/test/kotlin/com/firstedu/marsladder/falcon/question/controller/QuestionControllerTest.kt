package com.firstedu.marsladder.falcon.question.controller

import com.firstedu.marsladder.falcon.client.fuxi.dto.QuestionOption
import com.firstedu.marsladder.falcon.question.QuestionCAS
import com.firstedu.marsladder.falcon.question.QuestionDifficulty
import com.firstedu.marsladder.falcon.question.service.QuestionService
import com.firstedu.marsladder.falcon.question.service.domain.Question
import com.firstedu.marsladder.falcon.security.FalconJwtAuthenticationTokenConverter
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.security.WebSecurityConfig
import com.firstedu.marsladder.falcon.user.WebMvcTestConfiguration
import com.firstedu.marsladder.falcon.utils.XssFilter
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.time.LocalDateTime

@WebMvcTest(
    controllers = [QuestionController::class],
    excludeFilters = [
        ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = [
                FalconJwtAuthenticationTokenConverter::class,
            ],
        ),
    ],
)
@Import(WebSecurityConfig::class, WebMvcTestConfiguration::class)
class QuestionControllerTest {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockBean
    private lateinit var questionService: QuestionService

    @MockBean
    private lateinit var sessionProvider: SessionProvider

    @MockBean
    private lateinit var xssFilter: XssFilter

    private val fakeUserId = "fakeUserCognitoUid"

    @BeforeEach
    internal fun setUp() {
        whenever(sessionProvider.getUserId()).thenReturn(fakeUserId)
    }

    @Test
    @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
    internal fun `return 200  when given question id and user id `() {
        val questionId = "12343242"
        val question = buildQuestion()
        `when`(questionService.getQuestionDetail(fakeUserId, questionId)).thenReturn(question)
        mockMvc
            .perform(
                MockMvcRequestBuilders.get("/questions/$questionId")
                    .accept(MediaType.APPLICATION_JSON),
            )
            .andExpect(MockMvcResultMatchers.status().isOk)
        verify(questionService, times(1)).getQuestionDetail(fakeUserId, questionId)
    }

    @Test
    @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["TEACHER"])
    internal fun `return 200  when given question id and user id and role is teacher `() {
        val questionId = "12343242"
        val question = buildQuestion()
        `when`(questionService.getQuestionDetail(fakeUserId, questionId)).thenReturn(question)
        mockMvc
            .perform(
                MockMvcRequestBuilders.get("/questions/$questionId")
                    .accept(MediaType.APPLICATION_JSON),
            )
            .andExpect(MockMvcResultMatchers.status().isOk)
        verify(questionService, times(1)).getQuestionDetail(fakeUserId, questionId)
    }

    @Test
    @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["TUTOR"])
    internal fun `return 200  when given question id and user id and role is tutor `() {
        val questionId = "12343242"
        val question = buildQuestion()
        `when`(questionService.getQuestionDetail(fakeUserId, questionId)).thenReturn(question)
        mockMvc
            .perform(
                MockMvcRequestBuilders.get("/questions/$questionId")
                    .accept(MediaType.APPLICATION_JSON),
            )
            .andExpect(MockMvcResultMatchers.status().isOk)
        verify(questionService, times(1)).getQuestionDetail(fakeUserId, questionId)
    }

    private fun buildQuestion(): Question {
        val question =
            Question(
                topic = "Your Topic",
                answerAnalysis = "Your Answer Analysis",
                courseId = "Your Course ID",
                questionOptions =
                    listOf(
                        QuestionOption(
                            id = "",
                            value = "",
                            correct = true,
                        ),
                    ),
                contentIds = listOf("contentId1", "contentId2"),
                difficulty = QuestionDifficulty.EASY,
                cas = QuestionCAS.FREE,
                createdBy = "Creator",
                modifiedBy = "Modifier",
                reviewedBy = "Reviewer",
                createdByEmail = "<EMAIL>",
                modifiedByEmail = "<EMAIL>",
                reviewedByEmail = "<EMAIL>",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                reviewed = true,
                deleted = false,
                examStyle = true,
                correctCount = 10,
                answeredCount = 20,
                answeredStudentCount = 15,
                questionTagId = null,
                questionTag = null,
            )
        return question
    }
}

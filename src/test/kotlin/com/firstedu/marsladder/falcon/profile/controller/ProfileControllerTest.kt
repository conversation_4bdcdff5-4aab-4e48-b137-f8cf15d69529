package com.firstedu.marsladder.falcon.profile.controller

import com.firstedu.marsladder.falcon.profile.controller.dto.CreateProfileResponse
import com.firstedu.marsladder.falcon.profile.controller.dto.GetProfileResponse
import com.firstedu.marsladder.falcon.profile.controller.dto.UpdateProfileRequest
import com.firstedu.marsladder.falcon.profile.service.ProfileService
import com.firstedu.marsladder.falcon.profile.service.domain.Profile
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.utils.levelUpExperience
import com.nhaarman.mockitokotlin2.doNothing
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import org.springframework.http.HttpStatus

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
internal class ProfileControllerTest {
    @InjectMocks
    private lateinit var profileController: ProfileController

    @Mock
    private lateinit var profileService: ProfileService

    @Mock
    private lateinit var sessionProvider: SessionProvider

    private val mockCognitoUid = "cognito uid"
    private val mockEmail = "<EMAIL>"

    @BeforeEach
    fun setup() {
        whenever(sessionProvider.getUserId()).thenReturn(mockCognitoUid)
        whenever(sessionProvider.getEmail()).thenReturn(mockEmail)
    }

    @Test
    internal fun `should return id when save profile successfully`() {
        val mockProfile =
            Profile(
                id = "mock_id",
                cognitoUid = mockCognitoUid,
                nickname = "ranD0mN1ckNam3",
                avatar = "https://mock.com/default.png",
                gold = 1,
                experience = 1,
            )
        whenever(profileService.saveProfile(mockCognitoUid, mockEmail)).thenReturn(mockProfile)
        val responseEntity = profileController.saveProfile()
        Assertions.assertEquals(responseEntity.statusCode, HttpStatus.CREATED)
        Assertions.assertEquals(responseEntity.body as CreateProfileResponse, CreateProfileResponse("mock_id"))
    }

    @Test
    internal fun `should return profile information when get profile successfully`() {
        val mockProfile =
            Profile(
                id = "mock_id",
                cognitoUid = "mock_cognitoUid",
                nickname = "ranD0mN1ckNam3",
                avatar = "https://mock.com/default.png",
                gold = 1,
                experience = 1,
                level = 1,
            )
        whenever(profileService.getProfileByCognitoUid(mockCognitoUid)).thenReturn(mockProfile)
        val responseEntity = profileController.getProfile()
        Assertions.assertEquals(responseEntity.statusCode, HttpStatus.OK)
        Assertions.assertEquals(
            responseEntity.body as GetProfileResponse,
            GetProfileResponse(
                id = "mock_id",
                cognitoUid = "mock_cognitoUid",
                nickname = "ranD0mN1ckNam3",
                avatar = "https://mock.com/default.png",
                gold = 1,
                experience = 1,
                level = 1,
                levelUpExperience = levelUpExperience(1),
            ),
        )
    }

    @Test
    internal fun `should return updated profile when update profile successfully`() {
        val updateProfileRequest =
            UpdateProfileRequest(
                nickname = "ranD0mN1ckNam3",
                avatar = "2.jpg",
            )
        doNothing().whenever(profileService)
            .updateProfile(
                mockCognitoUid,
                mockEmail,
                Profile(
                    nickname = updateProfileRequest.nickname,
                    avatar = updateProfileRequest.avatar,
                ),
            )
        val responseEntity = profileController.updateProfile(updateProfileRequest)
        Assertions.assertEquals(responseEntity.statusCode, HttpStatus.NO_CONTENT)
    }
}

package com.firstedu.marsladder.falcon.following.controller

import com.firstedu.marsladder.falcon.following.service.FollowService
import com.firstedu.marsladder.falcon.following.service.FollowServiceEntity
import com.firstedu.marsladder.falcon.following.service.FolloweeProfileServiceEntity
import com.firstedu.marsladder.falcon.security.FalconJwtAuthenticationTokenConverter
import com.firstedu.marsladder.falcon.user.WebMvcTestConfiguration
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.whenever
import org.hamcrest.Matchers.containsString
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.time.LocalDateTime

@WebMvcTest(
    FollowController::class,
    excludeFilters = [
        ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = [
                FalconJwtAuthenticationTokenConverter::class,
            ],
        ),
    ],
)
@Import(WebMvcTestConfiguration::class)
internal class FollowControllerGetFollowsTest {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockBean
    private lateinit var followService: FollowService

    @Test
    @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
    internal fun `responds 200 with follows`() {
        // Prepare
        val followerCognitoUid = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf"
        val followeeCognitoUid = "67f211f6-4a08-4a42-ae14-8f6ba6685ece"
        val follow =
            FollowServiceEntity(
                id = "0e9c49a6-**************-c4bda9415452",
                followerCognitoUid = followerCognitoUid,
                followeeCognitoUid = followeeCognitoUid,
                createdAt = LocalDateTime.parse("2021-04-17T13:00:30.471367"),
                followeeRemark = "",
                followee =
                    FolloweeProfileServiceEntity(
                        nickname = "Tony Stark",
                        level = 10,
                        coins = 99999,
                        avatarUrl = "https://static.test.marsladder.com.au/avatars/1.jpg",
                    ),
            )
        val pageIndex = 0
        val pageSize = 2
        val order = Sort.Order(Sort.Direction.DESC, "createdAt")
        val pageable = PageRequest.of(pageIndex, pageSize, Sort.by(order))
        val page = PageImpl(listOf(follow), PageRequest.of(pageIndex, pageSize), 1)

        whenever(
            followService.getFollows(
                eq(followerCognitoUid),
                eq(pageable),
            ),
        ).thenReturn(page)

        // Run and Verify
        val expectedResponseBody =
            "{\"id\":\"0e9c49a6-**************-c4bda9415452\",\"followerCognitoUid\":\"b89b0d17-4f15-4366-a6f6-398c7b0a38cf\",\"followeeCognitoUid\":\"67f211f6-4a08-4a42-ae14-8f6ba6685ece\",\"createdAt\":\"2021-04-17T13:00:30.471367\",\"followeeRemark\":\"\",\"followee\":{\"nickname\":\"Tony Stark\",\"level\":10,\"coins\":99999,\"avatarUrl\":\"https://static.test.marsladder.com.au/avatars/1.jpg\"}}"
        mockMvc.perform(
            MockMvcRequestBuilders.get("/users/me/follows")
                .queryParam("number", "0")
                .queryParam("size", "2")
                .queryParam("sortFields", "createdAt")
                .queryParam("sortDirection", "desc")
                .accept(MediaType.APPLICATION_JSON),
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.content().string(containsString(expectedResponseBody)))
    }
}

package com.firstedu.marsladder.falcon.marketing

import com.firstedu.marsladder.falcon.TestMockNonJpaBeansConfig
import com.firstedu.marsladder.falcon.config.ApplicationConfig
import com.firstedu.marsladder.falcon.klaviyo.profiles.KlaviyoProfile
import com.firstedu.marsladder.falcon.klaviyo.profiles.createprofilefunction.KlaviyoProfileDuplicatedException
import com.firstedu.marsladder.falcon.klaviyo.profiles.updateprofilefunction.KlaviyoUpdateProfileRequest
import com.firstedu.marsladder.falcon.user.activity.UserActivityRepositoryEntity
import com.firstedu.marsladder.falcon.user.identity.repository.IdentityRepository
import com.firstedu.marsladder.falcon.user.identity.repository.IdentityRepositoryEntity
import com.firstedu.marsladder.falcon.user.identity.repository.UserRepository
import com.firstedu.marsladder.falcon.user.identity.repository.UserRepositoryEntity
import com.firstedu.marsladder.falcon.user.identity.repository.UserToKlaviyoLog
import com.firstedu.marsladder.falcon.user.identity.repository.UserToKlaviyoLogRepository
import com.firstedu.marsladder.falcon.user.profile.service.UserProfile
import com.firstedu.marsladder.falcon.user.profile.service.UserProfileService
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@ActiveProfiles("h2")
@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Import(
    UserToKlaviyoServiceImpl::class,
    ApplicationConfig::class,
    TestMockNonJpaBeansConfig::class,
)
@TestPropertySource(
    properties = [
        "klaviyo.enabled=true",
    ],
)
internal class UserToKlaviyoServiceImplTest {
    @Autowired
    lateinit var userCreationPublicationServiceImpl: UserToKlaviyoServiceImpl

    @Autowired
    lateinit var userRepository: UserRepository

    @Autowired
    lateinit var identityRepository: IdentityRepository

    @Autowired
    lateinit var userToKlaviyoLogRepository: UserToKlaviyoLogRepository

    @Autowired
    lateinit var entityManager: TestEntityManager

    @MockBean
    lateinit var klaviyoClient: com.firstedu.marsladder.falcon.klaviyo.KlaviyoClient

    @MockBean
    lateinit var userProfileService: UserProfileService

    @MockBean
    lateinit var klaviyoProfileBuilder: KlaviyoProfileBuilder

    @BeforeEach
    fun beforeEach() {
        identityRepository.deleteAll()
        userRepository.deleteAll()
        userToKlaviyoLogRepository.deleteAll()
    }

    @Test
    internal fun `given user1 doesn't exist, user2 exists and user3 has been sent and user 4 is failed to send then create user 1 and and update user 2`() {
        // prepare
        val teacherPosition = "teacher"

        // prepare user 1
        val userRepositoryEntity1 = UserRepositoryEntity(email = "<EMAIL>", identities = emptyList())
        entityManager.persistAndFlush(userRepositoryEntity1)
        val userActivityRepositoryEntity1 =
            UserActivityRepositoryEntity(userId = userRepositoryEntity1.id!!, activatedAt = LocalDateTime.now())
        entityManager.persistAndFlush(userActivityRepositoryEntity1)
        val userIdentityRepositoryEntity1 =
            IdentityRepositoryEntity(
                cognitoUid = "cognitoUid1",
                cognitoUsername = "cognitoUsername1",
                email = userRepositoryEntity1.email,
                user = userRepositoryEntity1,
            )
        entityManager.persistAndFlush(userIdentityRepositoryEntity1)
        val user1Profile =
            UserProfile(
                userId = userRepositoryEntity1.id!!,
                phoneNumber = "110112119",
                isEverLoggedIn = true,
                consumerName = "consumerName",
                isTeacher = true,
                teacherName = "teacherName",
                isTutor = false,
                tutorFirstName = null,
                tutorLastName = null,
                isCurrentlySubscribed = true,
                isPreviouslySubscribed = true,
                teacherPosition = teacherPosition,
                invitedStudentCount = 1,
                createdTaskCount = 10,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(userProfileService.getUserProfile(userRepositoryEntity1.id!!)).thenReturn(user1Profile)
        val user1KlaviyoProfile =
            KlaviyoProfile(
                userRepositoryEntity1.email,
                phoneNumber = "110112119",
                country = null,
                "firstName",
                "teacher",
                user1Profile.isEverLoggedIn,
                user1Profile.isCurrentlySubscribed,
                user1Profile.isPreviouslySubscribed,
                user1Profile.teacherPosition,
                invitedStudentCount = 1,
                user1Profile.createdTaskCount,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(klaviyoProfileBuilder.build(userRepositoryEntity1.email, user1Profile)).thenReturn(user1KlaviyoProfile)

        // prepare user 2
        val klaviyoProfileId = "klaviyoProfileId2"
        val userRepositoryEntity2 = UserRepositoryEntity(email = "<EMAIL>", identities = emptyList())
        entityManager.persistAndFlush(userRepositoryEntity2)
        val userActivityRepositoryEntity2 =
            UserActivityRepositoryEntity(userId = userRepositoryEntity2.id!!, activatedAt = LocalDateTime.now())
        entityManager.persistAndFlush(userActivityRepositoryEntity2)
        val userIdentityRepositoryEntity2 =
            IdentityRepositoryEntity(
                cognitoUid = "cognitoUid2",
                cognitoUsername = "cognitoUsername2",
                email = userRepositoryEntity2.email,
                user = userRepositoryEntity2,
            )
        entityManager.persistAndFlush(userIdentityRepositoryEntity2)
        val user2Profile =
            UserProfile(
                userId = userRepositoryEntity2.id!!,
                phoneNumber = "110112119",
                isEverLoggedIn = true,
                consumerName = "consumerName",
                isTeacher = true,
                teacherName = "teacherName",
                isTutor = false,
                tutorFirstName = null,
                tutorLastName = null,
                isCurrentlySubscribed = true,
                isPreviouslySubscribed = true,
                teacherPosition = teacherPosition,
                invitedStudentCount = 1,
                createdTaskCount = 10,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(userProfileService.getUserProfile(userRepositoryEntity2.id!!)).thenReturn(user2Profile)
        val user2KlaviyoProfile =
            KlaviyoProfile(
                userRepositoryEntity2.email,
                phoneNumber = "110112119",
                country = null,
                "firstName",
                "teacher",
                isEverLoggedIn = user2Profile.isEverLoggedIn,
                isCurrentlySubscribed = user2Profile.isCurrentlySubscribed,
                isPreviouslySubscribed = user2Profile.isPreviouslySubscribed,
                teacherPosition = user2Profile.teacherPosition,
                invitedStudentCount = 1,
                createdTaskCount = 10,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(klaviyoProfileBuilder.build(userRepositoryEntity2.email, user2Profile)).thenReturn(user2KlaviyoProfile)
        whenever(
            klaviyoClient.createProfile(user2KlaviyoProfile),
        ).thenThrow(KlaviyoProfileDuplicatedException(klaviyoProfileId, RuntimeException()))

        // prepare user 3
        val userRepositoryEntity3 = UserRepositoryEntity(email = "<EMAIL>", identities = emptyList())
        entityManager.persistAndFlush(userRepositoryEntity3)
        val userActivityRepositoryEntity3 =
            UserActivityRepositoryEntity(userId = userRepositoryEntity3.id!!, activatedAt = LocalDateTime.now())
        entityManager.persistAndFlush(userActivityRepositoryEntity3)
        val userIdentityRepositoryEntity3 =
            IdentityRepositoryEntity(
                cognitoUid = "cognitoUid3",
                cognitoUsername = "cognitoUsername3",
                email = userRepositoryEntity3.email,
                user = userRepositoryEntity3,
            )
        entityManager.persistAndFlush(userIdentityRepositoryEntity3)
        val user3Profile =
            UserProfile(
                userId = userRepositoryEntity3.id!!,
                phoneNumber = "110112119",
                isEverLoggedIn = true,
                consumerName = "consumerName",
                isTeacher = true,
                teacherName = "teacherName",
                isTutor = false,
                tutorFirstName = null,
                tutorLastName = null,
                isCurrentlySubscribed = true,
                isPreviouslySubscribed = true,
                teacherPosition = teacherPosition,
                invitedStudentCount = 1,
                createdTaskCount = 10,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(userProfileService.getUserProfile(userRepositoryEntity3.id!!)).thenReturn(user3Profile)
        val user3KlaviyoProfile =
            KlaviyoProfile(
                userRepositoryEntity3.email,
                phoneNumber = "110112119",
                country = null,
                "firstName",
                "teacher",
                user3Profile.isEverLoggedIn,
                user3Profile.isCurrentlySubscribed,
                user3Profile.isPreviouslySubscribed,
                user3Profile.teacherPosition,
                1,
                createdTaskCount = user3Profile.createdTaskCount,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(klaviyoProfileBuilder.build(userRepositoryEntity3.email, user3Profile)).thenReturn(user3KlaviyoProfile)
        val user3ToKlaviyoLog = UserToKlaviyoLog(userId = userRepositoryEntity3.id!!, batch = batchCalculator())
        entityManager.persistAndFlush(user3ToKlaviyoLog)

        // prepare user 4
        val userRepositoryEntity4 = UserRepositoryEntity(email = "<EMAIL>", identities = emptyList())
        entityManager.persistAndFlush(userRepositoryEntity4)
        val userActivityRepositoryEntity4 =
            UserActivityRepositoryEntity(userId = userRepositoryEntity4.id!!, activatedAt = LocalDateTime.now())
        entityManager.persistAndFlush(userActivityRepositoryEntity4)
        val user4Profile =
            UserProfile(
                userId = userRepositoryEntity4.id!!,
                phoneNumber = "110112119",
                isEverLoggedIn = true,
                consumerName = "consumerName",
                isTeacher = true,
                teacherName = "teacherName",
                isTutor = false,
                tutorFirstName = null,
                tutorLastName = null,
                isCurrentlySubscribed = true,
                isPreviouslySubscribed = true,
                teacherPosition = teacherPosition,
                invitedStudentCount = 1,
                createdTaskCount = 10,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(userProfileService.getUserProfile(userRepositoryEntity4.id!!)).thenReturn(user4Profile)
        val user4KlaviyoProfile =
            KlaviyoProfile(
                userRepositoryEntity4.email,
                phoneNumber = "110112119",
                country = null,
                "firstName",
                role = "teacher",
                isEverLoggedIn = user4Profile.isEverLoggedIn,
                isCurrentlySubscribed = user4Profile.isCurrentlySubscribed,
                isPreviouslySubscribed = user4Profile.isPreviouslySubscribed,
                teacherPosition = user4Profile.teacherPosition,
                invitedStudentCount = 1,
                createdTaskCount = 10,
                answeredQuestionCount = 15,
                hasReceivedVoucher = true,
                voucherValue = BigDecimal.TEN,
            )
        whenever(klaviyoProfileBuilder.build(userRepositoryEntity4.email, user4Profile)).thenReturn(user4KlaviyoProfile)
        whenever(klaviyoClient.createProfile(user4KlaviyoProfile)).thenThrow(RuntimeException())

        // run
        userCreationPublicationServiceImpl.process(10)

        // verify
        verify(klaviyoClient, times(1)).createProfile(user1KlaviyoProfile)

        verify(klaviyoClient, times(1)).createProfile(user2KlaviyoProfile)
        val user2KlaviyoUpdateProfileRequest =
            KlaviyoUpdateProfileRequest(
                klaviyoProfileId,
                KlaviyoProfile(
                    userRepositoryEntity2.email,
                    phoneNumber = "110112119",
                    country = null,
                    "firstName",
                    "teacher",
                    user2Profile.isEverLoggedIn,
                    user2Profile.isCurrentlySubscribed,
                    user2Profile.isPreviouslySubscribed,
                    user2Profile.teacherPosition,
                    1,
                    user2Profile.createdTaskCount,
                    answeredQuestionCount = 15,
                    hasReceivedVoucher = true,
                    voucherValue = BigDecimal.TEN,
                ),
            )
        verify(klaviyoClient, times(1)).updateProfile(user2KlaviyoUpdateProfileRequest)

        verify(klaviyoClient, times(0)).createProfile(user3KlaviyoProfile)

        val eventLogs = userToKlaviyoLogRepository.findAll()
        assertThat(eventLogs.size).isEqualTo(4)

        val batch = LocalDate.now().format(DateTimeFormatter.ISO_DATE)

        val user1EventLog = eventLogs.find { it.userId == userRepositoryEntity1.id }
        assertThat(user1EventLog).isNotNull
        assertThat(user1EventLog!!.batch).isEqualTo(batch)

        val user2EventLog = eventLogs.find { it.userId == userRepositoryEntity2.id }
        assertThat(user2EventLog).isNotNull
        assertThat(user2EventLog!!.batch).isEqualTo(batch)

        val user4EventLog = eventLogs.find { it.userId == userRepositoryEntity4.id }
        assertThat(user4EventLog).isNotNull
        assertThat(user4EventLog!!.batch).isEqualTo(batch)
    }

    private fun batchCalculator(): String {
        return LocalDate.now().format(DateTimeFormatter.ISO_DATE)
    }
}

name: PR Check

on:
  pull_request:
    branches:
      - main

jobs:
  code_style:
    runs-on: ubuntu-latest
    name: Code Style

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: Check code style
        run: ./gradlew ktlintCheck

  build_artifact:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      # doc: https://github.com/gradle/actions/blob/main/docs/setup-gradle.md#build-with-a-specific-gradle-version
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          gradle-version: wrapper

      - name: Build artifact
        run: ./gradlew assemble testClasses --parallel --build-cache

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: falcon-jar
          path: build/

  unit_test:
    name: Test Shard ${{ matrix.shard }} / ${{ matrix.total-shards }}
    runs-on: ubuntu-latest
    needs: build_artifact

    # Define the matrix for parallel jobs
    strategy:
      fail-fast: true
      matrix:
        total-shards: [ 5 ] # Define total number of shards
        shard: [ 1, 2, 3, 4, 5 ] # Create a job for each shard index

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'
          cache: 'gradle'

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: falcon-jar
          path: build/

      - name: Create Test Shard File
        run: ./auto/shard-tests.sh ${{ matrix.shard }} ${{ matrix.total-shards }} tests-for-this-shard.txt

      - name: Run Tests for Shard
        # Pass the file with test patterns to our Gradle build
        run: ./gradlew test -x classes -x compileKotlin -x compileTestKotlin -x ProcessResources -PtestIncludesFile=tests-for-this-shard.txt

  integration_test:
    runs-on: ubuntu-latest
    name: Integration Test
    if: ${{ false }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: Run integration tests
        run: ./auto/run-integration-tests

  db_migration_validation:
    runs-on: ubuntu-latest
    name: DB Migration Validation

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: Validate DB migration
        run: |
          ./auto/validate-db-migration
